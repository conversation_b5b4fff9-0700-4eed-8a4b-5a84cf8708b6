{"version": 3, "file": "Artifacts-CZ-0nd5-.js", "sources": ["../../src/pages/Artifacts.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"artifacts\">\n    <template #header>\n      <PageHeadingArtifacts />\n    </template>\n\n    <ArtifactCollections />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import {\n    PageHeadingArtifacts,\n    ArtifactCollections\n  } from '@prefecthq/prefect-ui-library'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  usePageTitle('Artifacts')\n</script>\n"], "names": ["usePageTitle"], "mappings": "mLAiBE,OAAAA,EAAa,WAAW"}