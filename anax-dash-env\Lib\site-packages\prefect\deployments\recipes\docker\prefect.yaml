prefect-version: null
name: null

description: "Store code within a custom docker image alongside its runtime environment"

required_inputs:
  image_name: "The image name, including repository, to give the built Docker image"
  tag: "The tag to give the built Docker image"

build:
  - prefect_docker.deployments.steps.build_docker_image:
      id: "build_image"
      requires: "prefect-docker>=0.3.1"
      image_name: "{{ image_name }}"
      tag: "{{ tag }}"
      dockerfile: "{{ dockerfile }}"

push: 
  - prefect_docker.deployments.steps.push_docker_image:
      requires: "prefect-docker>=0.3.1"
      image_name: "{{ build_image.image_name }}"
      tag: "{{ build_image.tag }}"

pull: 
  - prefect.deployments.steps.set_working_directory:
      directory: "/opt/prefect/{{ name }}"

deployments:
  - name: null
    version: null
    tags: []
    description: null
    schedule: {}
    flow_name: null
    entrypoint: null
    parameters: {}
    work_pool:
      name: null
      work_queue_name: null
      job_variables: 
        # uses the `image` output from the `build_image` step
        image: "{{ build_image.image }}"