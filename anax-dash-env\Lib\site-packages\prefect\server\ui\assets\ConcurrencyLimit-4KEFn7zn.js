import{d as h,h as x,W as R,u as g,j as i,aI as D,al as I,bt as P,i as S,k as u,c,o as n,l as s,m as T,p as t,a6 as B,a as r,cO as N,cP as m,cQ as O,be as V}from"./index-CUm6gmtO.js";import{u as W}from"./usePageTitle-BEMSydIZ.js";const U=h({__name:"ConcurrencyLimit",setup(j){const p=x(),y=R("concurrencyLimitId"),d=g(),v=i(()=>[{label:"Details",hidden:D.xl},{label:"Active Task Runs"}]),a=I("tab","Details"),{tabs:_}=P(v,a),b={interval:3e5},L=S(p.concurrencyLimits.getConcurrencyLimit,[y.value],b),e=i(()=>L.response);function f(){d.push(V.concurrencyLimits())}const C=i(()=>e.value?`Concurrency Limit: ${e.value.tag}`:"Concurrency Limit");return W(C),(A,l)=>{const k=u("p-tabs"),w=u("p-layout-well");return n(),c(w,{class:"concurrencyLimit"},{header:s(()=>[e.value?(n(),c(t(O),{key:0,"concurrency-limit":e.value,onDelete:f},null,8,["concurrency-limit"])):r("",!0)]),well:s(()=>[e.value?(n(),c(t(m),{key:0,alternate:"","concurrency-limit":e.value},null,8,["concurrency-limit"])):r("",!0)]),default:s(()=>[T(k,{selected:t(a),"onUpdate:selected":l[0]||(l[0]=o=>B(a)?a.value=o:null),tabs:t(_)},{details:s(()=>[e.value?(n(),c(t(m),{key:0,"concurrency-limit":e.value},null,8,["concurrency-limit"])):r("",!0)]),"active-task-runs":s(()=>{var o;return[(o=e.value)!=null&&o.activeSlots?(n(),c(t(N),{key:0,"active-slots":e.value.activeSlots},null,8,["active-slots"])):r("",!0)]}),_:1},8,["selected","tabs"])]),_:1})}}});export{U as default};
//# sourceMappingURL=ConcurrencyLimit-4KEFn7zn.js.map
