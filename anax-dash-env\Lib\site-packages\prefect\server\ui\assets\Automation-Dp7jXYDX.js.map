{"version": 3, "file": "Automation-Dp7jXYDX.js", "sources": ["../../src/pages/Automation.vue"], "sourcesContent": ["<template>\n  <p-layout-default v-if=\"automation\" class=\"automation\">\n    <template #header>\n      <PageHeading :crumbs=\"crumbs\">\n        <template #actions>\n          <AutomationToggle :automation=\"automation\" @update=\"subscription.refresh\" />\n          <AutomationMenu :automation=\"automation\" @delete=\"goToAutomations\" />\n        </template>\n      </PageHeading>\n    </template>\n    <p-content>\n      <p-key-value label=\"Description\" :value=\"automation.description\" />\n\n      <p-content secondary>\n        <span class=\"automation-card__label\">Trigger</span>\n        <AutomationTriggerDescription :trigger=\"automation.trigger\" />\n      </p-content>\n\n      <p-content secondary>\n        <span class=\"automation-card__label\">{{ toPluralString('Action', automation.actions.length) }}</span>\n        <template v-for=\"action in automation.actions\" :key=\"action.id\">\n          <p-card><AutomationActionDescription :action=\"action\" /></p-card>\n        </template>\n      </p-content>\n    </p-content>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { Crumb } from '@prefecthq/prefect-design'\n  import { PageHeading, AutomationMenu, AutomationToggle, AutomationTriggerDescription, AutomationActionDescription, toPluralString, useWorkspaceRoutes } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam, useSubscription } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { useRouter } from 'vue-router'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { usePrefectApi } from '@/compositions/usePrefectApi'\n\n  const routes = useWorkspaceRoutes()\n  const router = useRouter()\n  const api = usePrefectApi()\n  const automationId = useRouteParam('automationId')\n\n  const subscription = useSubscription(api.automations.getAutomation, [automationId])\n  const automation = computed(() => subscription.response)\n\n  const name = computed(() => automation.value?.name ?? '')\n\n  const crumbs = computed<Crumb[]>(() => [\n    { text: 'Automations', to: routes.automations() },\n    { text: name.value },\n  ])\n\n  const title = computed<string>(() => {\n    if (automation.value) {\n      return `Automation: ${automation.value.name}`\n    }\n\n    return 'Automation'\n  })\n\n  usePageTitle(title)\n\n  function goToAutomations(): void {\n    router.push(routes.automations())\n  }\n</script>"], "names": ["routes", "useWorkspaceRoutes", "router", "useRouter", "api", "usePrefectApi", "automationId", "useRouteParam", "subscription", "useSubscription", "automation", "computed", "name", "_a", "crumbs", "title", "usePageTitle", "goToAutomations"], "mappings": "qaAqCE,MAAMA,EAASC,EAAmB,EAC5BC,EAASC,EAAU,EACnBC,EAAMC,EAAc,EACpBC,EAAeC,EAAc,cAAc,EAE3CC,EAAeC,EAAgBL,EAAI,YAAY,cAAe,CAACE,CAAY,CAAC,EAC5EI,EAAaC,EAAS,IAAMH,EAAa,QAAQ,EAEjDI,EAAOD,EAAS,IAAA,OAAM,QAAAE,EAAAH,EAAW,QAAX,YAAAG,EAAkB,OAAQ,GAAE,EAElDC,EAASH,EAAkB,IAAM,CACrC,CAAE,KAAM,cAAe,GAAIX,EAAO,aAAc,EAChD,CAAE,KAAMY,EAAK,KAAM,CAAA,CACpB,EAEKG,EAAQJ,EAAiB,IACzBD,EAAW,MACN,eAAeA,EAAW,MAAM,IAAI,GAGtC,YACR,EAEDM,EAAaD,CAAK,EAElB,SAASE,GAAwB,CACxBf,EAAA,KAAKF,EAAO,aAAa,CAAA"}