import{d as m,h as p,i as d,j as a,k,c as s,o as t,l as c,t as _,a as f,F as i,p as o,cf as h,cg as y,m as B,ch as C}from"./index-CUm6gmtO.js";import{u as g}from"./usePageTitle-BEMSydIZ.js";const F=m({__name:"Blocks",setup(x){const n=p(),e=d(n.blockDocuments.getBlockDocumentsCount),l=a(()=>e.executed&&e.response==0),r=a(()=>e.executed);return g("Blocks"),(D,b)=>{const u=k("p-layout-default");return t(),s(u,{class:"blocks"},{header:c(()=>[B(o(C))]),default:c(()=>[r.value?(t(),_(i,{key:0},[l.value?(t(),s(o(h),{key:0})):(t(),s(o(y),{key:1,onDelete:o(e).refresh},null,8,["onDelete"]))],64)):f("",!0)]),_:1})}}});export{F as default};
//# sourceMappingURL=Blocks-DMsddS1F.js.map
