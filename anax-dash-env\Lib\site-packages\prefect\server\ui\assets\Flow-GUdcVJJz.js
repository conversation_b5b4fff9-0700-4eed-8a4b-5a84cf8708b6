import{d as R,bP as C,j as t,aR as B,aS as D,f as b,t as N,o as a,n as T,m as n,p as e,bQ as U,aW as V,h as W,W as j,u as E,al as P,i as $,bu as z,bR as M,k as F,c,l as o,a as d,a6 as O,bz as Q,bS as Z,bT as q,bU as A,be as G}from"./index-CUm6gmtO.js";import{s as H}from"./index-B4HswuBc.js";import{u as J}from"./usePageTitle-BEMSydIZ.js";const K={class:"flow-stats"},L={class:"flow-stats__cards"},X=R({__name:"FlowStats",props:{flowId:{}},setup(w){const f=w,r={range:{type:"span",seconds:-604800}},{flowId:u}=C(f),i=t(()=>({flowId:u.value,range:r.range}));B(D,{interval:H(30)});const l=t(()=>b.map("FlowStatsFilter",i.value,"FlowRunsFilter")),p=t(()=>b.map("FlowStatsFilter",i.value,"TaskRunsFilter"));return(_,m)=>(a(),N("div",K,[T("div",L,[n(e(U),{filter:l.value},null,8,["filter"]),n(e(V),{filter:p.value},null,8,["filter"])])]))}}),le=R({__name:"Flow",setup(w){const f=W(),r=j("flowId"),u=t(()=>[r.value]),i=E(),l=P("tab","Runs"),p=["Runs","Deployments","Details"],_={interval:3e5},m=$(f.flows.getFlow,[r.value],_),s=t(()=>m.response),{filter:y}=z({flows:{id:u}}),{filter:k}=M({flows:{id:u}});function x(){i.push(G.flows())}const S=t(()=>s.value?`Flow: ${s.value.name}`:"Flow");return J(S),(Y,v)=>{const g=F("p-tabs"),h=F("p-layout-default");return a(),c(h,{class:"flow"},{header:o(()=>[s.value?(a(),c(e(A),{key:0,flow:s.value,onDelete:x},null,8,["flow"])):d("",!0)]),default:o(()=>[s.value?(a(),c(X,{key:0,"flow-id":s.value.id},null,8,["flow-id"])):d("",!0),n(g,{selected:e(l),"onUpdate:selected":v[0]||(v[0]=I=>O(l)?l.value=I:null),tabs:p},{details:o(()=>[s.value?(a(),c(e(q),{key:0,flow:s.value},null,8,["flow"])):d("",!0)]),deployments:o(()=>[n(e(Z),{filter:e(k),prefix:"deployments"},null,8,["filter"])]),runs:o(()=>[n(e(Q),{filter:e(y),selectable:"",prefix:"runs"},null,8,["filter"])]),_:1},8,["selected"])]),_:1})}}});export{le as default};
//# sourceMappingURL=Flow-GUdcVJJz.js.map
