{"version": 3, "file": "Notifications-DfHA2foZ.js", "sources": ["../../src/pages/Notifications.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"notifications\">\n    <template #header>\n      <PageHeadingNotifications />\n    </template>\n    <p-message info>\n      Notifications are deprecated and will be migrated in the future. Please use Automations.\n    </p-message>\n    <template v-if=\"loaded\">\n      <template v-if=\"empty\">\n        <NotificationsPageEmptyState />\n      </template>\n      <template v-else>\n        <NotificationsTable :notifications=\"notifications\" @delete=\"notificationsSubscription.refresh()\" @update=\"notificationsSubscription.refresh()\" />\n      </template>\n    </template>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { Notification, NotificationsTable, NotificationsPageEmptyState, PageHeadingNotifications, useWorkspaceApi } from '@prefecthq/prefect-ui-library'\n  import { useSubscription } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const api = useWorkspaceApi()\n  const notificationsSubscription = useSubscription(api.notifications.getNotifications)\n  const notifications = computed<Notification[]>(() => notificationsSubscription.response ?? [])\n  const empty = computed(() => notificationsSubscription.executed && notifications.value.length === 0)\n  const loaded = computed(() => notificationsSubscription.executed)\n\n  usePageTitle('Notifications')\n</script>\n"], "names": ["api", "useWorkspaceApi", "notificationsSubscription", "useSubscription", "notifications", "computed", "empty", "loaded", "usePageTitle"], "mappings": "qPAyBE,MAAMA,EAAMC,EAAgB,EACtBC,EAA4BC,EAAgBH,EAAI,cAAc,gBAAgB,EAC9EI,EAAgBC,EAAyB,IAAMH,EAA0B,UAAY,CAAA,CAAE,EACvFI,EAAQD,EAAS,IAAMH,EAA0B,UAAYE,EAAc,MAAM,SAAW,CAAC,EAC7FG,EAASF,EAAS,IAAMH,EAA0B,QAAQ,EAEhE,OAAAM,EAAa,eAAe"}