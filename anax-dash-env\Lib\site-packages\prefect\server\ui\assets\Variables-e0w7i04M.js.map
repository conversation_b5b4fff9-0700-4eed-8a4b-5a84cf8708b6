{"version": 3, "file": "Variables-e0w7i04M.js", "sources": ["../../src/pages/Variables.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"variables\">\n    <template #header>\n      <PageHeadingVariables @create=\"refresh\" />\n    </template>\n    <template v-if=\"loaded\">\n      <template v-if=\"empty\">\n        <VariablesPageEmptyState @create=\"refresh\" />\n      </template>\n      <template v-else>\n        <VariablesTable ref=\"table\" @delete=\"refresh\" @update=\"refresh\" />\n      </template>\n    </template>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { localization, PageHeadingVariables, VariablesTable, VariablesPageEmptyState, useWorkspaceApi } from '@prefecthq/prefect-ui-library'\n  import { useSubscription } from '@prefecthq/vue-compositions'\n  import { ref, computed } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const table = ref<typeof VariablesTable>()\n  const refresh = (): void => {\n    variablesSubscription.value.refresh()\n    table.value?.refreshSubscriptions()\n  }\n  const api = useWorkspaceApi()\n\n  const variablesSubscription = computed(() => useSubscription(api.variables.getVariables))\n  const empty = computed(() => variablesSubscription.value.executed && variablesSubscription.value.response?.length === 0)\n  const loaded = computed(() => variablesSubscription.value.executed)\n  usePageTitle(localization.info.variables)\n</script>"], "names": ["table", "ref", "refresh", "variablesSubscription", "_a", "api", "useWorkspaceApi", "computed", "useSubscription", "empty", "loaded", "usePageTitle", "localization"], "mappings": "yPAsBE,MAAMA,EAAQC,EAA2B,EACnCC,EAAU,IAAY,OAC1BC,EAAsB,MAAM,QAAQ,GACpCC,EAAAJ,EAAM,QAAN,MAAAI,EAAa,sBACf,EACMC,EAAMC,EAAgB,EAEtBH,EAAwBI,EAAS,IAAMC,EAAgBH,EAAI,UAAU,YAAY,CAAC,EAClFI,EAAQF,EAAS,IAAM,OAAA,OAAAJ,EAAsB,MAAM,YAAYC,EAAAD,EAAsB,MAAM,WAA5B,YAAAC,EAAsC,UAAW,EAAC,EACjHM,EAASH,EAAS,IAAMJ,EAAsB,MAAM,QAAQ,EACrD,OAAAQ,EAAAC,EAAa,KAAK,SAAS"}