import{d as v,aR as y,aS as V,h as C,i as F,j as i,aT as x,k as d,c as _,o as n,l,t as m,a as R,m as a,F as S,p as e,aU as B,n as r,aV as N,aW as U,f as T,aX as A,aY as D,B as E,z as W,E as j,aZ as H,ay as P}from"./index-CUm6gmtO.js";import{s as Y}from"./index-B4HswuBc.js";const q={class:"workspace-dashboard__header-actions"},z={class:"workspace-dashboard__subflows-toggle"},I={key:1,class:"workspace-dashboard__grid"},M={class:"workspace-dashboard__side"},J=v({__name:"Dashboard",setup(O){y(V,{interval:Y(30)});const f=C(),c=F(f.flowRuns.getFlowRunsCount,[{}]),u=i(()=>c.executed),p=i(()=>c.response===0),b=[{text:"Dashboard"}],s=x({range:{type:"span",seconds:-86400},tags:[]}),k=()=>T.map("WorkspaceDashboardFilter",s,"TaskRunsFilter");return(X,o)=>{const g=d("p-toggle"),w=d("p-button"),h=d("p-layout-default");return n(),_(h,{class:"workspace-dashboard"},{header:l(()=>[a(e(W),{crumbs:b,class:"workspace-dashboard__page-heading"},j({_:2},[u.value&&!p.value?{name:"actions",fn:l(()=>[r("div",q,[r("div",z,[a(g,{modelValue:e(s).hideSubflows,"onUpdate:modelValue":o[0]||(o[0]=t=>e(s).hideSubflows=t),append:"Hide subflows"},null,8,["modelValue"])]),a(e(H),{selected:e(s).tags,"onUpdate:selected":o[1]||(o[1]=t=>e(s).tags=t),"empty-message":"All tags"},null,8,["selected"]),a(e(P),{modelValue:e(s).range,"onUpdate:modelValue":o[2]||(o[2]=t=>e(s).range=t),class:"workspace-dashboard__date-select"},null,8,["modelValue"])])]),key:"0"}:void 0]),1024)]),default:l(()=>[u.value?(n(),m(S,{key:0},[p.value?(n(),_(e(B),{key:0})):(n(),m("div",I,[a(e(N),{filter:e(s)},null,8,["filter"]),r("div",M,[a(e(U),{filter:k}),a(e(A),{class:"workspace-dashboard__work-pools",filter:e(s)},null,8,["filter"])])]))],64)):R("",!0),a(e(D),{title:"Ready to scale?",subtitle:"Webhooks, role and object-level security, and serverless push work pools on Prefect Cloud"},{actions:l(()=>[a(w,{to:"https://www.prefect.io/cloud-vs-oss?utm_source=oss&utm_medium=oss&utm_campaign=oss&utm_term=none&utm_content=none",target:"_blank",primary:""},{default:l(()=>o[3]||(o[3]=[E(" Upgrade to Cloud ")])),_:1})]),_:1})]),_:1})}}});export{J as default};
//# sourceMappingURL=Dashboard-D9Q_DHp_.js.map
