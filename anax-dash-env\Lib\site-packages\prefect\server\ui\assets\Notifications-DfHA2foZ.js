import{d as g,h as k,i as v,j as s,k as r,c as n,o as a,l as i,m as u,t as x,a as y,B as C,F as N,p as o,cw as w,cx as B,cy as V}from"./index-CUm6gmtO.js";import{u as b}from"./usePageTitle-BEMSydIZ.js";const T=g({__name:"Notifications",setup(h){const l=k(),t=v(l.notifications.getNotifications),c=s(()=>t.response??[]),f=s(()=>t.executed&&c.value.length===0),p=s(()=>t.executed);return b("Notifications"),(j,e)=>{const d=r("p-message"),m=r("p-layout-default");return a(),n(m,{class:"notifications"},{header:i(()=>[u(o(V))]),default:i(()=>[u(d,{info:""},{default:i(()=>e[2]||(e[2]=[C(" Notifications are deprecated and will be migrated in the future. Please use Automations. ")])),_:1}),p.value?(a(),x(N,{key:0},[f.value?(a(),n(o(w),{key:0})):(a(),n(o(B),{key:1,notifications:c.value,onDelete:e[0]||(e[0]=_=>o(t).refresh()),onUpdate:e[1]||(e[1]=_=>o(t).refresh())},null,8,["notifications"]))],64)):y("",!0)]),_:1})}}});export{T as default};
//# sourceMappingURL=Notifications-DfHA2foZ.js.map
