import{d as L,u as N,W as A,h as B,j as s,aI as V,al as j,bt as G,bF as f,bG as J,k as i,c,a as p,o as k,l as a,m as n,p as e,a6 as P,E as W,by as H,bH as K,bI as O,bJ as _,B as R,bK as U,v as X,G as Y,bL as Z,be as $}from"./index-CUm6gmtO.js";import{u as q}from"./usePageTitle-BEMSydIZ.js";const tt=L({__name:"TaskRun",setup(z){const m=N(),d=A("taskRunId"),v=B(),g=s(()=>[{label:"Details",hidden:V.xl},{label:"Logs"},{label:"Artifacts"},{label:"Task Inputs"}]),l=j("tab","Logs"),{tabs:w}=G(g,l),I=s(()=>d.value?[d.value]:null),h=f(v.taskRuns.getTaskRun,I,{interval:3e4}),t=s(()=>h.response),r=s(()=>{var u;return(u=t.value)==null?void 0:u.flowRunId}),y=s(()=>r.value?[r.value]:null),T=f(v.flowRuns.getFlowRun,y),b=s(()=>{var u;return(u=t.value)!=null&&u.taskInputs?JSON.stringify(t.value.taskInputs,void 0,2):"{}"});function x(){T.refresh(),m.push($.flowRun(r.value))}J(t);const D=s(()=>t.value?`Task Run: ${t.value.name}`:"Task Run");return q(D),(u,o)=>{const C=i("p-code-highlight"),F=i("p-tabs"),S=i("p-layout-well");return t.value?(k(),c(S,{key:0,class:"task-run"},{header:a(()=>[n(e(Z),{"task-run-id":t.value.id,onDelete:x},null,8,["task-run-id"])]),well:a(()=>[n(e(_),{alternate:"","task-run":t.value},null,8,["task-run"])]),default:a(()=>[n(F,{selected:e(l),"onUpdate:selected":o[0]||(o[0]=E=>P(l)?l.value=E:null),tabs:e(w)},W({details:a(()=>[n(e(_),{"task-run":t.value},null,8,["task-run"])]),logs:a(()=>[n(e(O),{"task-run":t.value},null,8,["task-run"])]),artifacts:a(()=>[t.value?(k(),c(e(K),{key:0,"task-run":t.value},null,8,["task-run"])):p("",!0)]),"task-inputs":a(()=>[t.value?(k(),c(e(H),{key:0,"text-to-copy":b.value},{default:a(()=>[n(C,{lang:"json",text:b.value,class:"task-run__inputs"},null,8,["text"])]),_:1},8,["text-to-copy"])):p("",!0)]),_:2},[t.value?{name:"task-inputs-heading",fn:a(()=>[o[1]||(o[1]=R(" Task inputs ")),n(e(U),{title:"Task Inputs"},{default:a(()=>[R(X(e(Y).info.taskInput),1)]),_:1})]),key:"0"}:void 0]),1032,["selected","tabs"])]),_:1})):p("",!0)}}});export{tt as default};
//# sourceMappingURL=TaskRun-VqxenMzl.js.map
