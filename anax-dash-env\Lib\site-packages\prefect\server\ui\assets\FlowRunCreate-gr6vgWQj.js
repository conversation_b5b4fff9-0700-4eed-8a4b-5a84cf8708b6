import{c9 as g,d as b,h as k,W as N,u as F,al as S,ca as x,ae as B,j as D,k as I,c as J,a as O,p as e,o as T,l as d,m as p,cb as P,cc as U,cd as V,ce as W,be as i,K as f,Y}from"./index-CUm6gmtO.js";import{u as j}from"./usePageTitle-BEMSydIZ.js";class E extends g{parse(t){return JSON.parse(decodeURIComponent(t??""))}format(t){return encodeURIComponent(JSON.stringify(t))}}const M=b({__name:"FlowRunCreate",setup(y){const t=k(),l=N("deploymentId"),c=F(),w=S("parameters",E,void 0),{deployment:a}=x(l),o=B(!1),R=async u=>{var m,s;if(!o.value)try{o.value=!0;const n=await t.deployments.createDeploymentFlowRun(l.value,u),r=((s=(m=u.state)==null?void 0:m.stateDetails)==null?void 0:s.scheduledTime)??void 0,C=!r,h=V(W,{flowRun:n,flowRunRoute:i.flowRun,router:c,immediate:C,startTime:r});f(h,"success"),c.push(i.deployment(l.value))}catch(n){const r=Y(n,"Something went wrong trying to create a flow run");f(r,"error"),console.error(n)}finally{o.value=!1}},_=()=>{c.back()},v=D(()=>a.value?`Create Flow Run for Deployment: ${a.value.name}`:"Create Flow Run for Deployment");return j(v),(u,m)=>{const s=I("p-layout-default");return e(a)?(T(),J(s,{key:0},{header:d(()=>[p(e(U),{deployment:e(a)},null,8,["deployment"])]),default:d(()=>[p(e(P),{deployment:e(a),parameters:e(w),disabled:o.value,onSubmit:R,onCancel:_},null,8,["deployment","parameters","disabled"])]),_:1})):O("",!0)}}});export{M as default};
//# sourceMappingURL=FlowRunCreate-gr6vgWQj.js.map
