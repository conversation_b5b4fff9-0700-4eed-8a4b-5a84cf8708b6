{"version": 3, "file": "AppRouterView-WtZ87VFj.js", "sources": ["../../src/components/JoinTheCommunityModal.vue", "../../src/components/ContextSidebar.vue", "../../src/compositions/useApiConfig.ts", "../../src/compositions/useCreateCan.ts", "../../src/compositions/useMobileMenuOpen.ts", "../../src/pages/AppRouterView.vue"], "sourcesContent": ["<template>\n  <p-modal v-model:show-modal=\"showModal\" title=\"Join the Prefect Community\">\n    <template #header>\n      <h2>Join the Community</h2>\n    </template>\n\n    <template #default>\n      <p>\n        Connect with 25k+ engineers scaling Python with Prefect. Show us your work and be the first to know about new Prefect features.\n      </p>\n\n      <div class=\"flex gap-x-2 items-center\">\n        <p-button primary icon=\"Slack\" :to=\"joinSlackUrl\" target=\"_blank\" @click=\"showJoinSlackThankYouMessage = true\">\n          Join us on Slack\n        </p-button>\n\n        <span v-if=\"showJoinSlackThankYouMessage\" class=\"text-sm italic\">\n          Thanks for joining our community!\n        </span>\n      </div>\n\n      <p-divider class=\"-my-3\" />\n\n      <p-form :id=\"formId\" @submit=\"signUpForEmailUpdates\">\n        <p-label v-slot=\"{ id }\" label=\"Notify me about Prefect updates\" :state :message=\"state.error\">\n          <p-text-input\n            :id\n            v-model=\"email\"\n            placeholder=\"<EMAIL>\"\n            :state\n          />\n        </p-label>\n      </p-form>\n\n      <p-message v-if=\"error\" error>\n        {{ error }}\n      </p-message>\n    </template>\n\n    <template #cancel=\"scope\">\n      <p-button class=\"sm:order-first\" @click=\"scope.close\">\n        Skip\n      </p-button>\n    </template>\n\n    <template #actions>\n      <p-button primary type=\"submit\" :form=\"formId\" :loading>\n        Sign up\n      </p-button>\n    </template>\n  </p-modal>\n</template>\n\n<script setup lang=\"ts\">\n  import { showToast } from '@prefecthq/prefect-design'\n  import { isEmail, isRequired } from '@prefecthq/prefect-ui-library'\n  import { useValidation } from '@prefecthq/vue-compositions'\n  import { ref } from 'vue'\n\n  const showModal = defineModel<boolean>('showModal')\n\n  const joinSlackUrl = 'http://prefect.io/slack?utm_source=oss&utm_medium=oss&utm_campaign=oss_popup&utm_term=none&utm_content=none'\n  const showJoinSlackThankYouMessage = ref(false)\n\n  const formId = 'join-the-community-modal'\n  const email = ref<string>()\n  const { validate, state } = useValidation(email, [isRequired('Email'), isEmail('Email')])\n\n  const loading = ref(false)\n  const error = ref('')\n\n  const formEndpoint = 'https://getform.io/f/eapderva'\n  async function signUpForEmailUpdates(): Promise<void> {\n    if (!await validate()) {\n      return\n    }\n\n    error.value = ''\n    loading.value = true\n    try {\n      await fetch(formEndpoint, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email: email.value }),\n        // getform redirects to a thank-you page so this cancels that additional request\n        redirect: 'manual',\n      })\n\n      showModal.value = false\n      showToast('Successfully subscribed', 'success')\n    } catch (err) {\n      error.value = 'An error occurred. Please try again.'\n      console.error(err)\n    } finally {\n      loading.value = false\n    }\n  }\n</script>", "<template>\n  <p-context-sidebar class=\"context-sidebar\">\n    <template #header>\n      <router-link :to=\"routes.root()\" class=\"context-sidebar__logo-link\">\n        <p-icon icon=\"Prefect\" class=\"context-sidebar__logo-icon\" />\n      </router-link>\n    </template>\n    <p-context-nav-item title=\"Dashboard\" :to=\"routes.dashboard()\" />\n    <p-context-nav-item title=\"Runs\" :to=\"routes.runs()\" />\n    <p-context-nav-item title=\"Flows\" :to=\"routes.flows()\" />\n    <p-context-nav-item title=\"Deployments\" :to=\"routes.deployments()\" />\n    <p-context-nav-item v-if=\"canSeeWorkPools\" title=\"Work Pools\" :to=\"routes.workPools()\" />\n    <p-context-nav-item v-if=\"!canSeeWorkPools\" title=\"Work Queues\" :to=\"routes.workQueues()\" />\n    <p-context-nav-item title=\"Blocks\" :to=\"routes.blocks()\" />\n    <p-context-nav-item :title=\"localization.info.variables\" :to=\"routes.variables()\" />\n    <p-context-nav-item title=\"Automations\" :to=\"routes.automations()\" />\n    <p-context-nav-item title=\"Event Feed\" :to=\"routes.events()\" />\n    <p-context-nav-item title=\"Notifications\" :to=\"routes.notifications()\" />\n    <p-context-nav-item title=\"Concurrency\" :to=\"routes.concurrencyLimits()\" />\n\n    <template #footer>\n      <a href=\"https://www.prefect.io/cloud-vs-oss?utm_source=oss&utm_medium=oss&utm_campaign=oss&utm_term=none&utm_content=none\" target=\"_blank\">\n        <p-context-nav-item>\n          <div>\n            Ready to scale?\n          </div>\n          <p-button primary small class=\"context-sidebar__upgade-button\">\n            Upgrade\n          </p-button>\n        </p-context-nav-item>\n      </a>\n\n      <p-context-nav-item @click=\"openJoinCommunityModal\">\n        Join the Community\n        <JoinTheCommunityModal :show-modal=\"showJoinCommunityModal || !joinTheCommunityModalDismissed\" @update:show-modal=\"updateShowModal\" />\n      </p-context-nav-item>\n\n      <p-context-nav-item title=\"Settings\" :to=\"routes.settings()\" />\n    </template>\n  </p-context-sidebar>\n</template>\n\n<script lang=\"ts\" setup>\n  import { PContextSidebar, PContextNavItem } from '@prefecthq/prefect-design'\n  import { localization, useShowModal } from '@prefecthq/prefect-ui-library'\n  import { useStorage } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import JoinTheCommunityModal from '@/components/JoinTheCommunityModal.vue'\n  import { useCan } from '@/compositions/useCan'\n  import { routes } from '@/router'\n\n  const can = useCan()\n  const canSeeWorkPools = computed(() => can.read.work_pool)\n\n  const { showModal: showJoinCommunityModal, open: openJoinCommunityModal } = useShowModal()\n  const { value: joinTheCommunityModalDismissed } = useStorage('local', 'join-the-community-modal-dismissed', false)\n  function updateShowModal(updatedShowModal: boolean): void {\n    showJoinCommunityModal.value = updatedShowModal\n    if (!updatedShowModal) {\n      joinTheCommunityModalDismissed.value = true\n    }\n  }\n</script>\n\n<style>\n.context-sidebar__logo-link { @apply\n  outline-none\n  rounded-md\n  focus:ring-spacing-focus-ring\n  focus:ring-focus-ring\n}\n\n.context-sidebar__logo-link:focus:not(:focus-visible) { @apply\n  ring-transparent\n}\n\n.context-sidebar__logo-icon { @apply\n  !w-[42px]\n  !h-[42px]\n}\n\n.context-sidebar__upgade-button { @apply\n  ml-auto\n}\n</style>", "import { PrefectConfig } from '@prefecthq/prefect-ui-library'\nimport { UiSettings } from '@/services/uiSettings'\nimport { MODE } from '@/utilities/meta'\n\nexport type UseWorkspaceApiConfig = {\n  config: PrefectConfig,\n}\nexport async function useApiConfig(): Promise<UseWorkspaceApiConfig> {\n  const baseUrl = await UiSettings.get('apiUrl')\n  const config: PrefectConfig = { baseUrl }\n\n  if (baseUrl.startsWith('/') && MODE() === 'development') {\n    config.baseUrl = `http://127.0.0.1:4200${baseUrl}`\n  }\n\n  return { config }\n}", "import { Can, createCan, workspacePermissions } from '@prefecthq/prefect-ui-library'\nimport { useSubscription } from '@prefecthq/vue-compositions'\nimport { computed, Ref } from 'vue'\nimport { uiSettings } from '@/services/uiSettings'\nimport { Permission } from '@/utilities/permissions'\n\ntype UseCreateCan = {\n  can: Can<Permission>,\n  pending: Ref<boolean>,\n}\n\nexport function useCreateCan(): UseCreateCan {\n  const flagsSubscription = useSubscription(uiSettings.getFeatureFlags, [])\n\n  const permissions = computed<Permission[]>(() => [\n    ...workspacePermissions,\n    ...flagsSubscription.response ?? [],\n  ])\n\n  const can = createCan(permissions)\n  const pending = computed(() => flagsSubscription.loading)\n\n  return {\n    can,\n    pending,\n  }\n}", "import { Ref, ref } from 'vue'\n\nexport type UseMobileMenuOpen = {\n  mobileMenuOpen: Ref<boolean>,\n  open: () => void,\n  close: () => void,\n  toggle: () => void,\n}\n\nexport function useMobileMenuOpen(): UseMobileMenuOpen {\n  const mobileMenuOpen = ref(false)\n\n  function toggle(): void {\n    mobileMenuOpen.value = !mobileMenuOpen.value\n  }\n\n  function open(): void {\n    mobileMenuOpen.value = true\n  }\n\n  function close(): void {\n    mobileMenuOpen.value = false\n  }\n\n  return {\n    mobileMenuOpen,\n    open,\n    close,\n    toggle,\n  }\n}", "<template>\n  <div class=\"app-router-view\">\n    <template v-if=\"!media.lg && !$route.meta.public\">\n      <PGlobalSidebar class=\"app-router-view__mobile-menu\">\n        <template #upper-links>\n          <router-link :to=\"appRoutes.root()\">\n            <p-icon icon=\"Prefect\" class=\"app-router-view__prefect-icon\" />\n          </router-link>\n        </template>\n        <template #bottom-links>\n          <p-button small icon=\"Bars3Icon\" class=\"app-router-view__menu-icon\" @click=\"toggle\" />\n        </template>\n      </PGlobalSidebar>\n    </template>\n    <ContextSidebar v-if=\"showMenu && !$route.meta.public\" class=\"app-router-view__sidebar\" @click=\"close\" />\n    <router-view :class=\"['app-router-view__view', { 'app-router-view__view--public': $route.meta.public }]\">\n      <template #default=\"{ Component }\">\n        <transition name=\"app-router-view-fade\" mode=\"out-in\">\n          <component :is=\"Component\" />\n        </transition>\n      </template>\n    </router-view>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\n  import { PGlobalSidebar, PIcon, media, showToast } from '@prefecthq/prefect-design'\n  import { workspaceApiKey, canKey as designCanKey, createWorkspaceRoutes, workspaceRoutesKey } from '@prefecthq/prefect-ui-library'\n  import { computed, provide, watchEffect } from 'vue'\n  import { RouterView } from 'vue-router'\n  import ContextSidebar from '@/components/ContextSidebar.vue'\n  import { useApiConfig } from '@/compositions/useApiConfig'\n  import { useCreateCan } from '@/compositions/useCreateCan'\n  import { useMobileMenuOpen } from '@/compositions/useMobileMenuOpen'\n  import router, { routes as appRoutes } from '@/router'\n  import { createPrefectApi, prefectApiKey } from '@/utilities/api'\n  import { canKey } from '@/utilities/permissions'\n  import { UiSettings } from '@/services/uiSettings'\n\n  const { can } = useCreateCan()\n  const { config } = await useApiConfig()\n  const api = createPrefectApi(config)\n  const routes = createWorkspaceRoutes()\n\n  provide(canKey, can)\n  provide(designCanKey, can)\n  provide(prefectApiKey, api)\n  provide(workspaceApiKey, api)\n  provide(workspaceRoutesKey, routes)\n\n\n  api.admin.authCheck().then(status_code => {\n    if (status_code == 401) {\n      if (router.currentRoute.value.name !== 'login') {\n        showToast('Authentication failed.', 'error', { timeout: false })\n        router.push({\n          name: 'login', \n          query: { redirect: router.currentRoute.value.fullPath }\n        })\n      }\n    } else {\n      api.health.isHealthy().then(healthy => {\n        if (!healthy) {\n          showToast(`Can't connect to Server API at ${config.baseUrl}. Check that it's accessible from your machine.`, 'error', { timeout: false })\n        }\n      })\n    }\n  })\n\n  const { mobileMenuOpen, toggle, close } = useMobileMenuOpen()\n  const showMenu = computed(() => media.lg || mobileMenuOpen.value)\n\n  watchEffect(() => document.body.classList.toggle('body-scrolling-disabled', showMenu.value && !media.lg))\n</script>\n\n<style>\n.body-scrolling-disabled { @apply\n  overflow-hidden\n}\n\n.app-router-view { @apply\n  flex\n  flex-col\n  bg-no-repeat\n  overflow-auto;\n  --prefect-scroll-margin: theme('spacing.20');\n  height: 100vh;\n  background-image: url('/decorative_iso-pixel-grid_light.svg');\n  background-attachment: fixed;\n  background-position: bottom -140px left -140px;\n}\n\n.dark .app-router-view {\n  background-image: url('/decorative_iso-pixel-grid_dark.svg');\n}\n\n.app-router-view__prefect-icon { @apply\n  w-7\n  h-7\n}\n\n.app-router-view__mobile-menu { @apply\n  h-auto\n  py-3\n}\n\n.app-router-view__sidebar { @apply\n  bg-floating\n  top-[54px]\n  lg:bg-transparent\n  lg:top-0\n}\n\n.app-router-view__sidebar .p-context-sidebar__header { @apply\n  hidden\n  lg:block\n}\n\n.app-router-view__view {\n  /* The 1px flex-basis is important because it allows us to use height: 100% without additional flexing */\n  flex: 1 0 1px;\n  height: 100%;\n}\n\n.app-router-view__view--public { @apply\n  flex\n  items-center\n  justify-center;\n  grid-column: 1 / -1;\n}\n\n@screen lg {\n  .app-router-view {\n    --prefect-scroll-margin: theme('spacing.2');\n    display: grid;\n    grid-template-columns: max-content minmax(0, 1fr);\n  }\n}\n\n.app-router-view-fade-enter-active,\n.app-router-view-fade-leave-active {\n  transition: opacity 0.25s ease;\n}\n\n.app-router-view-fade-enter-from,\n.app-router-view-fade-leave-to {\n  opacity: 0;\n}\n</style>"], "names": ["joinSlackUrl", "formId", "formEndpoint", "showModal", "_useModel", "__props", "showJoinSlackThankYouMessage", "ref", "email", "validate", "state", "useValidation", "isRequired", "isEmail", "loading", "error", "signUpForEmailUpdates", "showToast", "err", "can", "useCan", "canSeeWorkPools", "computed", "showJoinCommunityModal", "openJoinCommunityModal", "useShowModal", "joinTheCommunityModalDismissed", "useStorage", "updateShowModal", "updatedShowModal", "useApiConfig", "baseUrl", "UiSettings", "config", "MODE", "useCreateCan", "flagsSubscription", "useSubscription", "uiSettings", "permissions", "workspacePermissions", "createCan", "pending", "useMobileMenuOpen", "mobileMenuOpen", "toggle", "open", "close", "__temp", "__restore", "_withAsyncContext", "api", "createPrefectApi", "routes", "createWorkspaceRoutes", "provide", "can<PERSON>ey", "designCanKey", "prefectApiKey", "workspaceApiKey", "workspaceRoutesKey", "status_code", "router", "healthy", "showMenu", "media", "watchEffect"], "mappings": "qlBA6DQA,GAAe,8GAGfC,EAAS,2BAOTC,GAAe,gKAZf,MAAAC,EAAYC,EAAqBC,EAAA,WAAW,EAG5CC,EAA+BC,EAAI,EAAK,EAGxCC,EAAQD,EAAY,EACpB,CAAE,SAAAE,EAAU,MAAAC,CAAM,EAAIC,EAAcH,EAAO,CAACI,EAAW,OAAO,EAAGC,EAAQ,OAAO,CAAC,CAAC,EAElFC,EAAUP,EAAI,EAAK,EACnBQ,EAAQR,EAAI,EAAE,EAGpB,eAAeS,GAAuC,CAChD,GAAC,MAAMP,IAIX,CAAAM,EAAM,MAAQ,GACdD,EAAQ,MAAQ,GACZ,GAAA,CACF,MAAM,MAAMZ,GAAc,CACxB,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,UAAU,CAAE,MAAOM,EAAM,MAAO,EAE3C,SAAU,QAAA,CACX,EAEDL,EAAU,MAAQ,GAClBc,EAAU,0BAA2B,SAAS,QACvCC,EAAK,CACZH,EAAM,MAAQ,uCACd,QAAQ,MAAMG,CAAG,CAAA,QACjB,CACAJ,EAAQ,MAAQ,EAAA,EAClB,ymDC9CF,MAAMK,EAAMC,GAAO,EACbC,EAAkBC,EAAS,IAAMH,EAAI,KAAK,SAAS,EAEnD,CAAE,UAAWI,EAAwB,KAAMC,CAAA,EAA2BC,EAAa,EACnF,CAAE,MAAOC,GAAmCC,EAAW,QAAS,qCAAsC,EAAK,EACjH,SAASC,EAAgBC,EAAiC,CACxDN,EAAuB,MAAQM,EAC1BA,IACHH,EAA+B,MAAQ,GACzC,0jDCrDJ,eAAsBI,IAA+C,CACnE,MAAMC,EAAU,MAAMC,GAAW,IAAI,QAAQ,EACvCC,EAAwB,CAAE,QAAAF,CAAQ,EAExC,OAAIA,EAAQ,WAAW,GAAG,GAAKG,EAAA,EAIxB,CAAE,OAAAD,CAAO,CAClB,CCLO,SAASE,IAA6B,CAC3C,MAAMC,EAAoBC,EAAgBC,GAAW,gBAAiB,CAAA,CAAE,EAElEC,EAAcjB,EAAuB,IAAM,CAC/C,GAAGkB,EACH,GAAGJ,EAAkB,UAAY,CAAA,CAAC,CACnC,EAEKjB,EAAMsB,EAAUF,CAAW,EAC3BG,EAAUpB,EAAS,IAAMc,EAAkB,OAAO,EAEjD,MAAA,CACL,IAAAjB,EACA,QAAAuB,CACF,CACF,CCjBO,SAASC,IAAuC,CAC/C,MAAAC,EAAiBrC,EAAI,EAAK,EAEhC,SAASsC,GAAe,CACPD,EAAA,MAAQ,CAACA,EAAe,KAAA,CAGzC,SAASE,GAAa,CACpBF,EAAe,MAAQ,EAAA,CAGzB,SAASG,GAAc,CACrBH,EAAe,MAAQ,EAAA,CAGlB,MAAA,CACL,eAAAA,EACA,KAAAE,EACA,MAAAC,EACA,OAAAF,CACF,CACF,wFCSQ,KAAA,CAAE,IAAA1B,CAAI,EAAIgB,GAAa,EACvB,CAAE,OAAAF,CAAO,GAAI,CAAAe,EAAAC,CAAA,EAAAC,EAAA,IAAMpB,GAAa,CAAA,mBAChCqB,EAAMC,GAAiBnB,CAAM,EAC7BoB,EAASC,GAAsB,EAErCC,EAAQC,GAAQrC,CAAG,EACnBoC,EAAQE,GAActC,CAAG,EACzBoC,EAAQG,GAAeP,CAAG,EAC1BI,EAAQI,GAAiBR,CAAG,EAC5BI,EAAQK,GAAoBP,CAAM,EAGlCF,EAAI,MAAM,UAAY,EAAA,KAAoBU,GAAA,CACpCA,GAAe,IACbC,EAAO,aAAa,MAAM,OAAS,UACrC7C,EAAU,yBAA0B,QAAS,CAAE,QAAS,GAAO,EAC/D6C,EAAO,KAAK,CACV,KAAM,QACN,MAAO,CAAE,SAAUA,EAAO,aAAa,MAAM,QAAS,CAAA,CACvD,GAGHX,EAAI,OAAO,UAAY,EAAA,KAAgBY,GAAA,CAChCA,GACO9C,EAAA,kCAAkCgB,EAAO,OAAO,kDAAmD,QAAS,CAAE,QAAS,GAAO,CAC1I,CACD,CACH,CACD,EAED,KAAM,CAAE,eAAAW,EAAgB,OAAAC,EAAQ,MAAAE,CAAA,EAAUJ,GAAkB,EACtDqB,EAAW1C,EAAS,IAAM2C,EAAM,IAAMrB,EAAe,KAAK,EAEpD,OAAAsB,GAAA,IAAM,SAAS,KAAK,UAAU,OAAO,0BAA2BF,EAAS,OAAS,CAACC,EAAM,EAAE,CAAC"}