import{d as v,h,W as b,i as C,j as p,k as D,c as g,a as k,o as E,l as r,m as c,p as u,c5 as w,c8 as x,K as m,c7 as d,be as V,Y as B}from"./index-CUm6gmtO.js";import{u as I}from"./usePageTitle-BEMSydIZ.js";const Y=v({__name:"DeploymentEdit",setup(N){const a=h(),t=b("deploymentId"),i={interval:3e5},s=C(a.deployments.getDeployment,[t.value],i),e=p(()=>s.response);async function y(l){try{await a.deployments.updateDeploymentV2(t.value,l),m("Deployment updated","success"),s.refresh(),d.push(V.deployment(t.value))}catch(o){const n=B(o,"Error updating deployment");m(n,"error"),console.warn(o)}}function f(){d.back()}const _=p(()=>e.value?`Edit Deployment: ${e.value.name}`:"Edit Deployment");return I(_),(l,o)=>{const n=D("p-layout-default");return e.value?(E(),g(n,{key:0,class:"deployment-edit"},{header:r(()=>[c(u(x),{deployment:e.value},null,8,["deployment"])]),default:r(()=>[c(u(w),{deployment:e.value,onCancel:f,onSubmit:y},null,8,["deployment"])]),_:1})):k("",!0)}}});export{Y as default};
//# sourceMappingURL=DeploymentEdit-UHgYIcGf.js.map
