import{d as y,h,W as v,ae as w,I as N,k,c,o as r,l as u,a as x,p as f,cz as E,m as b,cB as g,c7 as l,be as p,K as B}from"./index-CUm6gmtO.js";import{u as I}from"./usePageTitle-BEMSydIZ.js";const A=y({__name:"NotificationEdit",async setup(z){let t,n;const i=h(),e=v("notificationId"),a=w({...([t,n]=N(()=>i.notifications.getNotification(e.value)),t=await t,n(),t)});async function d(s){try{await i.notifications.updateNotification(e.value,s),l.push(p.notifications())}catch(o){B("Error updating notification","error"),console.warn(o)}}function m(){l.push(p.notifications())}return I("Edit Notification"),(s,o)=>{const _=k("p-layout-default");return r(),c(_,null,{header:u(()=>[b(f(g))]),default:u(()=>[a.value?(r(),c(f(E),{key:0,notification:a.value,"onUpdate:notification":o[0]||(o[0]=C=>a.value=C),onSubmit:d,onCancel:m},null,8,["notification"])):x("",!0)]),_:1})}}});export{A as default};
//# sourceMappingURL=NotificationEdit-DQ129jPo.js.map
