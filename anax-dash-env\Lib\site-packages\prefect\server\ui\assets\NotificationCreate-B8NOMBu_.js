import{d as p,h as l,k as _,c as m,o as C,l as e,m as o,p as n,cz as d,cA as h,c7 as c,be as i,K as w}from"./index-CUm6gmtO.js";import{u as y}from"./usePageTitle-BEMSydIZ.js";const x=p({__name:"NotificationCreate",setup(N){const r=l();async function s(t){try{await r.notifications.createNotification(t),c.push(i.notifications())}catch(a){w("Error creating notification","error"),console.warn(a)}}function u(){c.push(i.notifications())}return y("Create Notification"),(t,a)=>{const f=_("p-layout-default");return C(),m(f,{class:"notification-create"},{header:e(()=>[o(n(h))]),default:e(()=>[o(n(d),{action:"Create",onSubmit:s,onCancel:u})]),_:1})}}});export{x as default};
//# sourceMappingURL=NotificationCreate-B8NOMBu_.js.map
