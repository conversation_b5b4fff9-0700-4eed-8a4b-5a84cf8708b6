import{d as A,V as y,u as b,W as w,I as g,j as h,k,c as v,o as C,l as a,m as u,p as e,z as U,U as V,G as r,B,K as d,Y as E}from"./index-CUm6gmtO.js";import{_ as I}from"./AutomationWizard.vue_vue_type_script_setup_true_lang-BNke21Sq.js";import{u as N}from"./usePageTitle-BEMSydIZ.js";import{u as P}from"./usePrefectApi-DO-y9p9U.js";import"./mapper-BXnlCs1t.js";import"./api-9onuHMbZ.js";const D=A({__name:"AutomationEdit",async setup(T){let o,m;const i=P(),c=y(),_=b(),p=w("automationId"),s=([o,m]=g(()=>i.automations.getAutomation(p.value)),o=await o,m(),o);N(`Edit Automation: ${s.name}`);const f=h(()=>[{text:"Automations",to:c.automations()},{text:s.name}]);async function x(l){try{await i.automations.updateAutomation(p.value,l),d(r.success.automationUpdate),_.push(c.automations())}catch(t){console.error(t);const n=E(t,r.error.automationUpdate);d(n,"error",{timeout:!1})}}return(l,t)=>{const n=k("p-layout-default");return C(),v(n,{class:"workspace-automation-create"},{header:a(()=>[u(e(U),{crumbs:f.value},{actions:a(()=>[u(e(V),{to:e(r).docs.automations},{default:a(()=>t[0]||(t[0]=[B(" Documentation ")])),_:1},8,["to"])]),_:1},8,["crumbs"])]),default:a(()=>[u(I,{automation:e(s),editing:"",onSubmit:x},null,8,["automation"])]),_:1})}}});export{D as default};
//# sourceMappingURL=AutomationEdit-B408SIb7.js.map
