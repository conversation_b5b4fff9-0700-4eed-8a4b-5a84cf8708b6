{"version": 3, "file": "WorkPoolQueueCreate-BY_r76RO.js", "sources": ["../../src/pages/WorkPoolQueueCreate.vue"], "sourcesContent": ["<template>\n  <p-layout-default>\n    <template #header>\n      <PageHeadingWorkPoolQueueCreate :work-pool-name=\"workPoolName\" />\n    </template>\n\n    <WorkPoolQueueCreateForm :work-pool-name=\"workPoolName\" />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { PageHeadingWorkPoolQueueCreate, WorkPoolQueueCreateForm } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam } from '@prefecthq/vue-compositions'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const workPoolName = useRouteParam('workPoolName')\n\n  usePageTitle('Create Work Pool Queue')\n</script>"], "names": ["workPoolName", "useRouteParam", "usePageTitle"], "mappings": "yMAeQ,MAAAA,EAAeC,EAAc,cAAc,EAEjD,OAAAC,EAAa,wBAAwB"}