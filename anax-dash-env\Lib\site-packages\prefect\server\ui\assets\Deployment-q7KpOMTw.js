import{d as P,W as E,j as r,u as V,h as j,i as z,aI as A,al as W,bt as Z,bu as f,bX as $,k as s,c,a as _,o as p,l,m as n,p as e,a6 as L,bz as b,t as M,F as O,B as X,bY as Y,bZ as v,b_ as q,b$ as G,c0 as H,c1 as J,c2 as K,c3 as Q,be as ee,c4 as te}from"./index-CUm6gmtO.js";import{u as ne}from"./usePageTitle-BEMSydIZ.js";const se=P({__name:"Deployment",setup(le){const m=E("deploymentId"),i=r(()=>[m.value]),g=V(),h=j(),w={interval:3e5},d=z(h.deployments.getDeployment,[m.value],w),t=r(()=>d.response),x=r(()=>{var a,o;return[{label:"Details",hidden:A.xl},{label:"Runs"},{label:"Upcoming"},{label:"Parameters",hidden:(a=t.value)==null?void 0:a.deprecated},{label:"Configuration",hidden:(o=t.value)==null?void 0:o.deprecated},{label:"Description"}]}),u=W("tab","Details"),{tabs:R}=Z(x,u);function k(){g.push(ee.deployments())}const{filter:D}=f({deployments:{id:i},flowRuns:{state:{name:te.filter(a=>a!=="Scheduled")}}}),{filter:U}=f({sort:"START_TIME_ASC",deployments:{id:i},flowRuns:{state:{name:["Scheduled"]}}}),{flowRun:y}=$(()=>({deployments:{id:i.value}})),C=r(()=>t.value?`Deployment: ${t.value.name}`:"Deployment");return ne(C),(a,o)=>{const T=s("p-content"),F=s("p-heading"),I=s("p-divider"),N=s("p-tabs"),S=s("p-layout-well");return t.value?(p(),c(S,{key:0,class:"deployment"},{header:l(()=>[n(e(Q),{deployment:t.value,onUpdate:e(d).refresh,onDelete:k},null,8,["deployment","onUpdate"])]),well:l(()=>[n(e(v),{deployment:t.value,alternate:"",onUpdate:e(d).refresh},null,8,["deployment","onUpdate"])]),default:l(()=>[n(N,{selected:e(u),"onUpdate:selected":o[0]||(o[0]=B=>L(u)?u.value=B:null),tabs:e(R)},{description:l(()=>[n(T,{secondary:""},{default:l(()=>[t.value.deprecated?(p(),c(e(H),{key:0})):t.value.description?(p(),c(e(J),{key:1,description:t.value.description},null,8,["description"])):(p(),c(e(K),{key:2,deployment:t.value},null,8,["deployment"]))]),_:1})]),parameters:l(()=>[n(e(G),{deployment:t.value},null,8,["deployment"])]),configuration:l(()=>[n(e(q),{deployment:t.value},null,8,["deployment"])]),details:l(()=>[n(e(v),{deployment:t.value,onUpdate:e(d).refresh},null,8,["deployment","onUpdate"])]),runs:l(()=>[e(y)?(p(),M(O,{key:0},[n(F,{heading:"6",class:"deployment__next-run"},{default:l(()=>o[1]||(o[1]=[X(" Next Run ")])),_:1}),n(e(Y),{"flow-run":e(y)},null,8,["flow-run"]),n(I)],64)):_("",!0),n(e(b),{filter:e(D),selectable:"",prefix:"runs"},null,8,["filter"])]),upcoming:l(()=>[n(e(b),{filter:e(U),selectable:"",prefix:"upcoming"},null,8,["filter"])]),_:1},8,["selected","tabs"])]),_:1})):_("",!0)}}});export{se as default};
//# sourceMappingURL=Deployment-q7KpOMTw.js.map
