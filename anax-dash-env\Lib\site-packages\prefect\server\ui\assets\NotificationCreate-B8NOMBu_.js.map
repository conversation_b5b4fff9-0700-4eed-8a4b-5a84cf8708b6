{"version": 3, "file": "NotificationCreate-B8NOMBu_.js", "sources": ["../../src/pages/NotificationCreate.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"notification-create\">\n    <template #header>\n      <PageHeadingNotificationCreate />\n    </template>\n    <NotificationForm action=\"Create\" @submit=\"submit\" @cancel=\"cancel\" />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { showToast } from '@prefecthq/prefect-design'\n  import { NotificationForm, Notification, PageHeadingNotificationCreate, NotificationCreate, useWorkspaceApi } from '@prefecthq/prefect-ui-library'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import router, { routes } from '@/router'\n\n  const api = useWorkspaceApi()\n\n  async function submit(notification: Partial<Notification>): Promise<void> {\n    try {\n      await api.notifications.createNotification(notification as NotificationCreate)\n      router.push(routes.notifications())\n    } catch (error) {\n      showToast('Error creating notification', 'error')\n      console.warn(error)\n    }\n  }\n\n  function cancel(): void {\n    router.push(routes.notifications())\n  }\n\n  usePageTitle('Create Notification')\n</script>"], "names": ["api", "useWorkspaceApi", "submit", "notification", "router", "routes", "error", "showToast", "cancel", "usePageTitle"], "mappings": "+NAeE,MAAMA,EAAMC,EAAgB,EAE5B,eAAeC,EAAOC,EAAoD,CACpE,GAAA,CACI,MAAAH,EAAI,cAAc,mBAAmBG,CAAkC,EACtEC,EAAA,KAAKC,EAAO,eAAe,QAC3BC,EAAO,CACdC,EAAU,8BAA+B,OAAO,EAChD,QAAQ,KAAKD,CAAK,CAAA,CACpB,CAGF,SAASE,GAAe,CACfJ,EAAA,KAAKC,EAAO,eAAe,CAAA,CAGpC,OAAAI,EAAa,qBAAqB"}