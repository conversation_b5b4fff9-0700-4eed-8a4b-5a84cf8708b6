{"version": 3, "file": "Blocks-DMsddS1F.js", "sources": ["../../src/pages/Blocks.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"blocks\">\n    <template #header>\n      <PageHeadingBlocks />\n    </template>\n    <template v-if=\"loaded\">\n      <template v-if=\"empty\">\n        <BlocksPageEmptyState />\n      </template>\n      <template v-else>\n        <BlockDocumentsTable @delete=\"subscription.refresh\" />\n      </template>\n    </template>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { PageHeadingBlocks, BlockDocumentsTable, BlocksPageEmptyState, useWorkspaceApi } from '@prefecthq/prefect-ui-library'\n  import { useSubscription } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const api = useWorkspaceApi()\n  const subscription = useSubscription(api.blockDocuments.getBlockDocumentsCount)\n  const empty = computed(() => subscription.executed && subscription.response == 0)\n  const loaded = computed(() => subscription.executed)\n\n  usePageTitle('Blocks')\n</script>"], "names": ["api", "useWorkspaceApi", "subscription", "useSubscription", "empty", "computed", "loaded", "usePageTitle"], "mappings": "kOAsBE,MAAMA,EAAMC,EAAgB,EACtBC,EAAeC,EAAgBH,EAAI,eAAe,sBAAsB,EACxEI,EAAQC,EAAS,IAAMH,EAAa,UAAYA,EAAa,UAAY,CAAC,EAC1EI,EAASD,EAAS,IAAMH,EAAa,QAAQ,EAEnD,OAAAK,EAAa,QAAQ"}