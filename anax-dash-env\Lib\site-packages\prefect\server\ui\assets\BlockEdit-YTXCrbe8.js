import{d as B,h as D,u as C,W as w,I as x,ae as p,k as g,c as E,a as I,p as e,o as F,l as c,m as s,cn as N,cu as P,_ as S,cp as T,cv as V,K as b,be as W}from"./index-CUm6gmtO.js";import{u as A}from"./usePageTitle-BEMSydIZ.js";const R=B({__name:"BlockEdit",async setup(H){let o,l;const n=D(),u=C(),r=w("blockDocumentId"),t=([o,l]=x(()=>n.blockDocuments.getBlockDocument(r.value)),o=await o,l(),o),{blockType:i,blockSchema:_}=t,d=p(t.data),m=p(t.name);function f(k){n.blockDocuments.updateBlockDocument(t.id,k).then(()=>{b("Block updated successfully","success"),u.push(W.block(r.value))}).catch(a=>{b("Failed to update block","error"),console.error(a)})}function y(){u.back()}return A(`Edit Block: ${m.value}`),(k,a)=>{const h=g("p-layout-default");return e(t)?(F(),E(h,{key:0,class:"block-edit"},{header:c(()=>[s(e(V),{"block-document":e(t)},null,8,["block-document"])]),default:c(()=>[s(e(N),{"block-type":e(i)},{default:c(()=>[s(e(P),S({data:d.value,"onUpdate:data":a[0]||(a[0]=v=>d.value=v)},{name:m.value,blockSchema:e(_)},T({submit:f,cancel:y})),null,16,["data"])]),_:1},8,["block-type"])]),_:1})):I("",!0)}}});export{R as default};
//# sourceMappingURL=BlockEdit-YTXCrbe8.js.map
