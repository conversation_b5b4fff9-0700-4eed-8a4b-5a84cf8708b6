import{d as c,h as _,W as u,I as k,k as m,c as w,o as d,l,m as r,p as e,d7 as i,d8 as f}from"./index-CUm6gmtO.js";import{u as P}from"./usePageTitle-BEMSydIZ.js";const N=c({__name:"WorkPoolEdit",async setup(C){let o,a;const s=_(),n=u("workPoolName"),t=([o,a]=k(()=>s.workPools.getWorkPoolByName(n.value)),o=await o,a(),o);return P("Create Work Pool"),(h,y)=>{const p=m("p-layout-default");return d(),w(p,null,{header:l(()=>[r(e(f),{"work-pool":e(t)},null,8,["work-pool"])]),default:l(()=>[r(e(i),{"work-pool":e(t)},null,8,["work-pool"])]),_:1})}}});export{N as default};
//# sourceMappingURL=WorkPoolEdit-5Pd6-Z4y.js.map
