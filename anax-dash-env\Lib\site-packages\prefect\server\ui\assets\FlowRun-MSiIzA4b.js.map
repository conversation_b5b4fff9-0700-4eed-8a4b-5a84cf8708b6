{"version": 3, "file": "FlowRun-MSiIzA4b.js", "sources": ["../../src/components/FlowRunGraphs.vue", "../../src/pages/FlowRun.vue"], "sourcesContent": ["<template>\n  <div class=\"flow-run-graphs\" :class=\"classes.root\">\n    <div class=\"flow-run-graphs__graph-panel-container\">\n      <div class=\"flow-run-graphs__graphs\">\n        <FlowRunGraph\n          v-model:fullscreen=\"fullscreen\"\n          v-model:viewport=\"dateRange\"\n          v-model:selected=\"selection\"\n          :flow-run\n          :fetch-events\n          class=\"flow-run-graphs__flow-run\"\n        />\n      </div>\n      <div class=\"flow-run-graphs__panel p-background\">\n        <FlowRunGraphSelectionPanel\n          v-if=\"selection?.kind === 'task-run' || selection?.kind === 'flow-run'\"\n          v-model:selection=\"selection\"\n          :floating=\"fullscreen\"\n        />\n      </div>\n    </div>\n    <FlowRunGraphEventPopover\n      v-if=\"selection && selection.kind === 'event'\"\n      v-model:selection=\"selection\"\n    />\n    <FlowRunGraphEventsPopover\n      v-if=\"selection && selection.kind === 'events'\"\n      v-model:selection=\"selection\"\n    />\n    <FlowRunGraphArtifactsPopover\n      v-if=\"selection && selection.kind === 'artifacts'\"\n      v-model:selection=\"selection\"\n    />\n    <FlowRunGraphStatePopover\n      v-if=\"selection?.kind === 'state'\"\n      v-model:selection=\"selection\"\n    />\n    <FlowRunGraphArtifactDrawer v-model:selection=\"selection\" />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\n  import {\n    FlowRunGraph,\n    RunGraphItemSelection,\n    RunGraphViewportDateRange,\n    FlowRun,\n    FlowRunGraphSelectionPanel,\n    FlowRunGraphArtifactDrawer,\n    FlowRunGraphArtifactsPopover,\n    FlowRunGraphStatePopover,\n    RunGraphFetchEventsContext,\n    FlowRunGraphEventPopover,\n    FlowRunGraphEventsPopover,\n    RunGraphEvent,\n    WorkspaceEventsFilter,\n    useWorkspaceApi\n  } from '@prefecthq/prefect-ui-library'\n  import { computed, ref } from 'vue'\n\n  defineProps<{\n    flowRun: FlowRun,\n  }>()\n\n  const api = useWorkspaceApi()\n  const dateRange = ref<RunGraphViewportDateRange>()\n\n  const fullscreen = ref(false)\n  const selection = ref<RunGraphItemSelection | null>(null)\n\n  const classes = computed(() => {\n    return {\n      root: {\n        'flow-run-graphs--fullscreen': fullscreen.value,\n        'flow-run-graphs--show-panel': Boolean(\n          selection.value?.kind === 'task-run'\n            || selection.value?.kind === 'flow-run',\n        ),\n      },\n    }\n  })\n\n  const fetchEvents = async ({ nodeId, since, until }: RunGraphFetchEventsContext): Promise<RunGraphEvent[]> => {\n    const filter: WorkspaceEventsFilter = {\n      anyResource: {\n        id: [`prefect.flow-run.${nodeId}`],\n      },\n      event: {\n        excludePrefix: ['prefect.log.write', 'prefect.task-run.'],\n      },\n      occurred: {\n        since,\n        until,\n      },\n    }\n\n    const { events } = await api.events.getEvents(filter)\n\n    return events\n  }\n</script>\n\n<style>\n.flow-run-graphs { @apply\n  relative;\n  --flow-run-graphs-panel-width: 320px;\n}\n\n.flow-run-graphs__graph-panel-container { @apply\n  relative\n  grid\n  grid-cols-1\n  gap-2\n  overflow-hidden\n}\n\n.flow-run-graphs--fullscreen { @apply\n  z-20\n  static\n}\n\n.flow-run-graphs__graphs { @apply\n  transition-[width];\n  width: 100%;\n}\n\n.flow-run-graphs--show-panel .flow-run-graphs__graphs {\n  width: calc(100% - var(--flow-run-graphs-panel-width) - theme(spacing.2));\n}\n\n.flow-run-graphs__flow-run { @apply\n  overflow-hidden\n  rounded\n}\n\n.flow-run-graphs__panel { @apply\n  absolute\n  right-0\n  top-0\n  bottom-0\n  translate-x-full\n  transition-transform\n  rounded;\n  width: var(--flow-run-graphs-panel-width)\n}\n\n.flow-run-graphs--fullscreen .flow-run-graphs__panel { @apply\n  bg-floating\n  top-4\n  right-4\n  bottom-auto\n}\n\n.flow-run-graphs--show-panel .flow-run-graphs__panel { @apply\n  translate-x-0\n}\n</style>\n", "<template>\n  <p-layout-default v-if=\"flowRun\" :key=\"flowRun.id\" class=\"flow-run\">\n    <template #header>\n      <PageHeadingFlowRun :flow-run-id=\"flowRun.id\" @delete=\"goToRuns\" />\n    </template>\n\n    <FlowRunGraphs v-if=\"!isPending\" :flow-run=\"flowRun\" />\n\n    <p-tabs v-model:selected=\"tab\" :tabs=\"tabs\">\n      <template #details>\n        <FlowRunDetails :flow-run=\"flowRun\" />\n      </template>\n\n      <template #logs>\n        <FlowRunLogs :flow-run=\"flowRun\" />\n      </template>\n\n      <template #artifacts>\n        <FlowRunArtifacts :flow-run=\"flowRun\" />\n      </template>\n\n      <template #task-runs>\n        <FlowRunTaskRuns :flow-run-id=\"flowRun.id\" />\n      </template>\n\n      <template #subflow-runs>\n        <FlowRunFilteredList :filter=\"subflowsFilter\" />\n      </template>\n\n      <template #parameters>\n        <CopyableWrapper :text-to-copy=\"parameters\">\n          <p-code-highlight lang=\"json\" :text=\"parameters\" class=\"flow-run__parameters\" />\n        </CopyableWrapper>\n      </template>\n\n      <template #job-variables>\n        <CopyableWrapper :text-to-copy=\"jobVariables\">\n          <p-code-highlight lang=\"json\" :text=\"jobVariables\" class=\"flow-run__job-variables\" />\n        </CopyableWrapper>\n      </template>\n    </p-tabs>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import {\n    PageHeadingFlowRun,\n    FlowRunArtifacts,\n    FlowRunDetails,\n    FlowRunLogs,\n    FlowRunTaskRuns,\n    FlowRunFilteredList,\n    useFlowRunFavicon,\n    CopyableWrapper,\n    isPendingStateType,\n    useTabs,\n    httpStatus,\n    useFlowRun,\n    useFlowRunsFilter,\n    stringify\n  } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam, useRouteQueryParam } from '@prefecthq/vue-compositions'\n  import { computed, watchEffect } from 'vue'\n  import { useRouter } from 'vue-router'\n  import FlowRunGraphs from '@/components/FlowRunGraphs.vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { routes } from '@/router'\n\n\n  const router = useRouter()\n  const flowRunId = useRouteParam('flowRunId')\n\n  const { flowRun, subscription: flowRunSubscription } = useFlowRun(flowRunId, { interval: 5000 })\n  const parameters = computed(() => stringify(flowRun.value?.parameters ?? {}))\n\n  const isPending = computed(() => {\n    return flowRun.value?.stateType ? isPendingStateType(flowRun.value.stateType) : true\n  })\n\n  const jobVariables = computed(() => stringify(flowRun.value?.jobVariables ?? {}))\n\n  const computedTabs = computed(() => [\n    { label: 'Logs' },\n    { label: 'Task Runs', hidden: isPending.value },\n    { label: 'Subflow Runs', hidden: isPending.value },\n    { label: 'Artifacts', hidden: isPending.value },\n    { label: 'Details' },\n    { label: 'Parameters' },\n    { label: 'Job Variables' },\n  ])\n  const tab = useRouteQueryParam('tab', 'Logs')\n  const { tabs } = useTabs(computedTabs, tab)\n\n  const parentFlowRunIds = computed(() => [flowRunId.value])\n  const { filter: subflowsFilter } = useFlowRunsFilter({\n    flowRuns: {\n      parentFlowRunId: parentFlowRunIds,\n    },\n  })\n\n  function goToRuns(): void {\n    router.push(routes.runs())\n  }\n\n  useFlowRunFavicon(flowRun)\n\n  const title = computed(() => {\n    if (!flowRun.value) {\n      return 'Flow Run'\n    }\n    return `Flow Run: ${flowRun.value.name}`\n  })\n  usePageTitle(title)\n\n  watchEffect(() => {\n    if (flowRunSubscription.error) {\n      const status = httpStatus(flowRunSubscription.error)\n\n      if (status.isInRange('clientError')) {\n        router.replace(routes[404]())\n      }\n    }\n  })\n</script>\n\n<style>\n.flow-run { @apply\n  items-start\n}\n\n.flow-run__logs { @apply\n  max-h-screen\n}\n\n.flow-run__header-meta { @apply\n  flex\n  gap-2\n  items-center\n  xl:hidden\n}\n\n.flow-run__job-variables,\n.flow-run__parameters { @apply\n  px-4\n  py-3\n}\n</style>\n"], "names": ["api", "useWorkspaceApi", "date<PERSON><PERSON><PERSON>", "ref", "fullscreen", "selection", "classes", "computed", "_a", "_b", "fetchEvents", "nodeId", "since", "until", "filter", "events", "router", "useRouter", "flowRunId", "useRouteParam", "flowRun", "flowRunSubscription", "useFlowRun", "parameters", "stringify", "isPending", "isPendingStateType", "jobVariables", "computedTabs", "tab", "useRouteQueryParam", "tabs", "useTabs", "parentFlowRunIds", "subflowsFilter", "useFlowRunsFilter", "goToRuns", "routes", "useFlowRunFavicon", "title", "usePageTitle", "watchEffect", "httpStatus"], "mappings": "4lBAgEE,MAAMA,EAAMC,EAAgB,EACtBC,EAAYC,EAA+B,EAE3CC,EAAaD,EAAI,EAAK,EACtBE,EAAYF,EAAkC,IAAI,EAElDG,EAAUC,EAAS,IAAM,SACtB,MAAA,CACL,KAAM,CACJ,8BAA+BH,EAAW,MAC1C,gCACEI,EAAAH,EAAU,QAAV,YAAAG,EAAiB,QAAS,cACrBC,EAAAJ,EAAU,QAAV,YAAAI,EAAiB,QAAS,UACjC,CAEJ,CAAA,CACD,EAEKC,EAAc,MAAO,CAAE,OAAAC,EAAQ,MAAAC,EAAO,MAAAC,KAAkE,CAC5G,MAAMC,EAAgC,CACpC,YAAa,CACX,GAAI,CAAC,oBAAoBH,CAAM,EAAE,CACnC,EACA,MAAO,CACL,cAAe,CAAC,oBAAqB,mBAAmB,CAC1D,EACA,SAAU,CACR,MAAAC,EACA,MAAAC,CAAA,CAEJ,EAEM,CAAE,OAAAE,CAAO,EAAI,MAAMf,EAAI,OAAO,UAAUc,CAAM,EAE7C,OAAAC,CACT,26CC9BA,MAAMC,EAASC,EAAU,EACnBC,EAAYC,EAAc,WAAW,EAErC,CAAE,QAAAC,EAAS,aAAcC,CAAoB,EAAIC,EAAWJ,EAAW,CAAE,SAAU,IAAM,EACzFK,EAAahB,EAAS,WAAMiB,OAAAA,IAAUhB,EAAAY,EAAQ,QAAR,YAAAZ,EAAe,aAAc,CAAA,CAAE,EAAC,EAEtEiB,EAAYlB,EAAS,IAAM,OAC/B,OAAOC,EAAAY,EAAQ,QAAR,MAAAZ,EAAe,UAAYkB,EAAmBN,EAAQ,MAAM,SAAS,EAAI,EAAA,CACjF,EAEKO,EAAepB,EAAS,WAAMiB,OAAAA,IAAUhB,EAAAY,EAAQ,QAAR,YAAAZ,EAAe,eAAgB,CAAA,CAAE,EAAC,EAE1EoB,EAAerB,EAAS,IAAM,CAClC,CAAE,MAAO,MAAO,EAChB,CAAE,MAAO,YAAa,OAAQkB,EAAU,KAAM,EAC9C,CAAE,MAAO,eAAgB,OAAQA,EAAU,KAAM,EACjD,CAAE,MAAO,YAAa,OAAQA,EAAU,KAAM,EAC9C,CAAE,MAAO,SAAU,EACnB,CAAE,MAAO,YAAa,EACtB,CAAE,MAAO,eAAgB,CAAA,CAC1B,EACKI,EAAMC,EAAmB,MAAO,MAAM,EACtC,CAAE,KAAAC,CAAS,EAAAC,EAAQJ,EAAcC,CAAG,EAEpCI,EAAmB1B,EAAS,IAAM,CAACW,EAAU,KAAK,CAAC,EACnD,CAAE,OAAQgB,CAAe,EAAIC,EAAkB,CACnD,SAAU,CACR,gBAAiBF,CAAA,CACnB,CACD,EAED,SAASG,GAAiB,CACjBpB,EAAA,KAAKqB,EAAO,MAAM,CAAA,CAG3BC,EAAkBlB,CAAO,EAEnB,MAAAmB,EAAQhC,EAAS,IAChBa,EAAQ,MAGN,aAAaA,EAAQ,MAAM,IAAI,GAF7B,UAGV,EACD,OAAAoB,GAAaD,CAAK,EAElBE,EAAY,IAAM,CACZpB,EAAoB,OACPqB,GAAWrB,EAAoB,KAAK,EAExC,UAAU,aAAa,GAChCL,EAAO,QAAQqB,EAAO,GAAG,EAAA,CAAG,CAEhC,CACD"}