{"version": 3, "file": "Artifact-CLM2loue.js", "sources": ["../../src/pages/Artifact.vue"], "sourcesContent": ["<template>\n  <p-layout-well class=\"artifact\">\n    <template #header>\n      <PageHeadingArtifact v-if=\"artifact\" :artifact=\"artifact\" />\n    </template>\n\n    <section v-if=\"artifact\">\n      <ArtifactDescription :artifact=\"artifact\" />\n\n      <p-divider />\n\n      <template v-if=\"media.xl\">\n        <p-content>\n          <ArtifactDataView :artifact=\"artifact\" />\n\n          <p-button class=\"artifact__raw-data-button\" small @click=\"showRaw = !showRaw\">\n            {{ showRaw ? 'Hide' : 'Show' }} raw data\n          </p-button>\n\n          <ArtifactDataRaw v-if=\"showRaw\" :artifact=\"artifact\" />\n        </p-content>\n      </template>\n\n      <template v-else>\n        <p-tabs v-model:selected=\"tab\" :tabs=\"tabs\">\n          <template #artifact>\n            <ArtifactDataView :artifact=\"artifact\" />\n          </template>\n\n          <template #details>\n            <ArtifactDetails :artifact=\"artifact\" />\n          </template>\n\n          <template #raw>\n            <ArtifactDataRaw :artifact=\"artifact\" />\n          </template>\n        </p-tabs>\n      </template>\n    </section>\n\n    <template #well>\n      <ArtifactDetails v-if=\"artifact\" :artifact=\"artifact\" alternate />\n    </template>\n  </p-layout-well>\n</template>\n\n<script lang=\"ts\" setup>\n  import { media } from '@prefecthq/prefect-design'\n  import {\n    PageHeadingArtifact,\n    ArtifactDataView,\n    ArtifactDescription,\n    ArtifactDetails,\n    ArtifactDataRaw,\n    localization,\n    capitalize,\n    useWorkspaceApi\n  } from '@prefecthq/prefect-ui-library'\n  import { useSubscription, useRouteParam, useRouteQueryParam } from '@prefecthq/vue-compositions'\n  import { computed, ref } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const api = useWorkspaceApi()\n  const artifactId = useRouteParam('artifactId')\n\n  const artifactSubscription = useSubscription(api.artifacts.getArtifact, [artifactId])\n  const artifact = computed(() => artifactSubscription.response)\n\n  const showRaw = ref(false)\n\n  const tabs = [\n    { label: 'Artifact' },\n    { label: 'Details' },\n    { label: 'Raw' },\n  ]\n  const tab = useRouteQueryParam('tab', 'Artifact')\n\n  const pageTitle = computed<string>(() => {\n    if (!artifact.value) {\n      return localization.info.artifact\n    }\n\n    return `${localization.info.artifact}: ${artifact.value.key ?? capitalize(artifact.value.type)}`\n  })\n\n  usePageTitle(pageTitle)\n</script>\n\n<style>\n.artifact__raw-data-button { @apply\n  mt-4\n  inline-block\n  mx-auto\n}\n</style>"], "names": ["api", "useWorkspaceApi", "artifactId", "useRouteParam", "artifactSubscription", "useSubscription", "artifact", "computed", "showRaw", "ref", "tabs", "tab", "useRouteQueryParam", "pageTitle", "localization", "capitalize", "usePageTitle"], "mappings": "2TA8DE,MAAMA,EAAMC,EAAgB,EACtBC,EAAaC,EAAc,YAAY,EAEvCC,EAAuBC,EAAgBL,EAAI,UAAU,YAAa,CAACE,CAAU,CAAC,EAC9EI,EAAWC,EAAS,IAAMH,EAAqB,QAAQ,EAEvDI,EAAUC,EAAI,EAAK,EAEnBC,EAAO,CACX,CAAE,MAAO,UAAW,EACpB,CAAE,MAAO,SAAU,EACnB,CAAE,MAAO,KAAM,CACjB,EACMC,EAAMC,EAAmB,MAAO,UAAU,EAE1CC,EAAYN,EAAiB,IAC5BD,EAAS,MAIP,GAAGQ,EAAa,KAAK,QAAQ,KAAKR,EAAS,MAAM,KAAOS,EAAWT,EAAS,MAAM,IAAI,CAAC,GAHrFQ,EAAa,KAAK,QAI5B,EAED,OAAAE,EAAaH,CAAS"}