import{d as N,h as R,W as D,i as U,j as s,aI as $,al as F,bt as I,bu as j,bR as q,k as m,c as i,a as d,o as k,l as a,m as t,p as e,a6 as S,bS as T,cZ as V,c_ as Z,bz as z,c$ as w,d0 as A,d1 as L}from"./index-CUm6gmtO.js";import{u as O}from"./usePageTitle-BEMSydIZ.js";const G=N({__name:"WorkPool",setup(Q){const b=R(),n=D("workPoolName"),f={interval:3e5},u=U(b.workPools.getWorkPoolByName,[n.value],f),o=s(()=>u.response),p=s(()=>{var l;return((l=o.value)==null?void 0:l.type)==="prefect-agent"}),v=s(()=>[{label:"Details",hidden:$.xl},{label:"Runs"},{label:"Work Queues"},{label:"Workers",hidden:p.value},{label:"Deployments"}]),r=F("tab","Details"),{tabs:_}=I(v,r),P=s(()=>{var l;return((l=o.value)==null?void 0:l.status)!=="ready"}),y=s(()=>{var l;return`prefect ${p.value?"agent":"worker"} start --pool "${(l=o.value)==null?void 0:l.name}"`}),{filter:W}=j({workPools:{name:[n.value]}}),{filter:h}=q({workPools:{name:[n.value]}}),C=s(()=>o.value?`Work Pool: ${o.value.name}`:"Work Pool");return O(C),(l,c)=>{const x=m("p-tabs"),g=m("p-layout-well");return o.value?(k(),i(g,{key:0,class:"work-pool"},{header:a(()=>[t(e(A),{"work-pool":o.value,onUpdate:e(u).refresh},null,8,["work-pool","onUpdate"]),P.value?(k(),i(e(L),{key:0,class:"work-pool__code-banner",command:y.value,title:"Your work pool is almost ready!",subtitle:"Run this command to start."},null,8,["command"])):d("",!0)]),well:a(()=>[t(e(w),{alternate:"","work-pool":o.value},null,8,["work-pool"])]),default:a(()=>[t(x,{selected:e(r),"onUpdate:selected":c[0]||(c[0]=B=>S(r)?r.value=B:null),tabs:e(_)},{details:a(()=>[t(e(w),{"work-pool":o.value},null,8,["work-pool"])]),runs:a(()=>[t(e(z),{filter:e(W),prefix:"runs"},null,8,["filter"])]),"work-queues":a(()=>[t(e(Z),{"work-pool-name":e(n)},null,8,["work-pool-name"])]),workers:a(()=>[t(e(V),{"work-pool-name":e(n)},null,8,["work-pool-name"])]),deployments:a(()=>[t(e(T),{filter:e(h)},null,8,["filter"])]),_:1},8,["selected","tabs"])]),_:1})):d("",!0)}}});export{G as default};
//# sourceMappingURL=WorkPool-B0R8Qlsu.js.map
