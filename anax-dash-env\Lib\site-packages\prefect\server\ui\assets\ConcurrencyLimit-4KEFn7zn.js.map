{"version": 3, "file": "ConcurrencyLimit-4KEFn7zn.js", "sources": ["../../src/pages/ConcurrencyLimit.vue"], "sourcesContent": ["<template>\n  <p-layout-well class=\"concurrencyLimit\">\n    <template #header>\n      <PageHeadingConcurrencyLimit v-if=\"concurrencyLimit\" :concurrency-limit=\"concurrencyLimit\" @delete=\"deleteConcurrencyLimit\" />\n    </template>\n\n    <p-tabs v-model:selected=\"tab\" :tabs=\"tabs\">\n      <template #details>\n        <ConcurrencyLimitDetails v-if=\"concurrencyLimit\" :concurrency-limit=\"concurrencyLimit\" />\n      </template>\n      <template #active-task-runs>\n        <ConcurrencyLimitActiveRuns v-if=\"concurrencyLimit?.activeSlots\" :active-slots=\"concurrencyLimit.activeSlots\" />\n      </template>\n    </p-tabs>\n\n    <template #well>\n      <ConcurrencyLimitDetails v-if=\"concurrencyLimit\" alternate :concurrency-limit=\"concurrencyLimit\" />\n    </template>\n  </p-layout-well>\n</template>\n\n<script lang=\"ts\" setup>\n  import { media } from '@prefecthq/prefect-design'\n  import { PageHeadingConcurrencyLimit, ConcurrencyLimitDetails, ConcurrencyLimitActiveRuns, useTabs, useWorkspaceApi } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam, useRouteQueryParam, useSubscription } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { useRouter } from 'vue-router'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { routes } from '@/router'\n\n  const api = useWorkspaceApi()\n  const concurrencyLimitId = useRouteParam('concurrencyLimitId')\n  const router = useRouter()\n\n  const computedTabs = computed(() => [\n    { label: 'Details', hidden: media.xl },\n    { label: 'Active Task Runs' },\n  ])\n  const tab = useRouteQueryParam('tab', 'Details')\n  const { tabs } = useTabs(computedTabs, tab)\n\n  const subscriptionOptions = {\n    interval: 300000,\n  }\n\n  const concurrencyLimitSubscription = useSubscription(api.concurrencyLimits.getConcurrencyLimit, [concurrencyLimitId.value], subscriptionOptions)\n  const concurrencyLimit = computed(() => concurrencyLimitSubscription.response)\n\n\n  function deleteConcurrencyLimit(): void {\n    router.push(routes.concurrencyLimits())\n  }\n\n  const title = computed<string>(() => {\n    if (!concurrencyLimit.value) {\n      return 'Concurrency Limit'\n    }\n\n    return `Concurrency Limit: ${concurrencyLimit.value.tag}`\n  })\n\n  usePageTitle(title)\n</script>"], "names": ["api", "useWorkspaceApi", "concurrencyLimitId", "useRouteParam", "router", "useRouter", "computedTabs", "computed", "media", "tab", "useRouteQueryParam", "tabs", "useTabs", "subscriptionOptions", "concurrencyLimitSubscription", "useSubscription", "concurrencyLimit", "deleteConcurrencyLimit", "routes", "title", "usePageTitle"], "mappings": "oRA8BE,MAAMA,EAAMC,EAAgB,EACtBC,EAAqBC,EAAc,oBAAoB,EACvDC,EAASC,EAAU,EAEnBC,EAAeC,EAAS,IAAM,CAClC,CAAE,MAAO,UAAW,OAAQC,EAAM,EAAG,EACrC,CAAE,MAAO,kBAAmB,CAAA,CAC7B,EACKC,EAAMC,EAAmB,MAAO,SAAS,EACzC,CAAE,KAAAC,CAAS,EAAAC,EAAQN,EAAcG,CAAG,EAEpCI,EAAsB,CAC1B,SAAU,GACZ,EAEMC,EAA+BC,EAAgBf,EAAI,kBAAkB,oBAAqB,CAACE,EAAmB,KAAK,EAAGW,CAAmB,EACzIG,EAAmBT,EAAS,IAAMO,EAA6B,QAAQ,EAG7E,SAASG,GAA+B,CAC/Bb,EAAA,KAAKc,EAAO,mBAAmB,CAAA,CAGlC,MAAAC,EAAQZ,EAAiB,IACxBS,EAAiB,MAIf,sBAAsBA,EAAiB,MAAM,GAAG,GAH9C,mBAIV,EAED,OAAAI,EAAaD,CAAK"}