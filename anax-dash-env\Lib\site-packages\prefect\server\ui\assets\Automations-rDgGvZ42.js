import{d as b,V as A,k as r,c,o as s,l as a,m as t,n as m,t as d,a as C,p as e,B as h,v as g,A as F,Q as P,q as T,C as $,y as q,J as z,F as k,i as E,j as y,D as J,Z as Q,z as S,E as Z,U as j,G as w}from"./index-CUm6gmtO.js";import{u as G}from"./usePageTitle-BEMSydIZ.js";import{u as I}from"./usePrefectApi-DO-y9p9U.js";import"./api-9onuHMbZ.js";import"./mapper-BXnlCs1t.js";const L={class:"automation-card__header"},H={class:"automation-card__header-actions"},K={key:0,class:"automation-card__description"},M={class:"automation-card__label"},O=b({__name:"AutomationCard",props:{automation:{}},emits:["update"],setup(V,{emit:p}){const l=p,_=A();return(o,n)=>{const f=r("p-link"),u=r("p-content"),v=r("p-card");return s(),c(v,{class:"automation-card"},{default:a(()=>[t(u,null,{default:a(()=>[t(u,{secondary:""},{default:a(()=>[m("div",L,[t(f,{class:"automation-card__name",to:e(_).automation(o.automation.id)},{default:a(()=>[h(g(o.automation.name),1)]),_:1},8,["to"]),m("div",H,[t(e(F),{automation:o.automation,onUpdate:n[0]||(n[0]=i=>l("update"))},null,8,["automation"]),t(e(P),{automation:o.automation,onDelete:n[1]||(n[1]=i=>l("update"))},null,8,["automation"])])]),o.automation.description?(s(),d("p",K,g(o.automation.description),1)):C("",!0)]),_:1}),t(u,{secondary:""},{default:a(()=>[n[2]||(n[2]=m("span",{class:"automation-card__label"},"Trigger",-1)),t(e(T),{trigger:o.automation.trigger},null,8,["trigger"])]),_:1}),t(u,{secondary:""},{default:a(()=>[m("span",M,g(e($)("Action",o.automation.actions.length)),1),(s(!0),d(k,null,q(o.automation.actions,i=>(s(),c(v,{key:i.id},{default:a(()=>[t(e(z),{action:i},null,8,["action"])]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})}}}),tt=b({__name:"Automations",setup(V){const p=A();G("Automations");const l=[{text:"Automations"}],_=I(),o=E(_.automations.getAutomations),n=y(()=>o.response??[]),f=y(()=>o.executed),u=y(()=>n.value.length===0);return(v,i)=>{const B=r("p-button"),U=r("p-virtual-scroller"),D=r("p-layout-default");return s(),c(D,{class:"automations"},{header:a(()=>[t(e(S),{crumbs:l},Z({"after-crumbs":a(()=>[t(B,{size:"sm",icon:"PlusIcon",to:e(p).automationCreate()},null,8,["to"])]),_:2},[u.value?void 0:{name:"actions",fn:a(()=>[t(e(j),{to:e(w).docs.automations},{default:a(()=>i[0]||(i[0]=[h(" Documentation ")])),_:1},8,["to"])]),key:"0"}]),1024)]),default:a(()=>[f.value?(s(),d(k,{key:0},[u.value?(s(),c(e(J),{key:0})):(s(),d(k,{key:1},[t(e(Q),{count:n.value.length,label:"automation"},null,8,["count"]),t(U,{items:n.value,class:"automations-list"},{default:a(({item:N})=>[t(O,{automation:N,onUpdate:e(o).refresh},null,8,["automation","onUpdate"])]),_:1},8,["items"])],64))],64)):C("",!0)]),_:1})}}});export{tt as default};
//# sourceMappingURL=Automations-rDgGvZ42.js.map
