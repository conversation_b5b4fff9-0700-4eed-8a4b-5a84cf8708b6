import{d as x,V as y,u as w,H as h,I as T,k as V,c as b,o as k,l as s,m as r,p as n,z as B,U as P,G as i,B as N,K as c,Y as U}from"./index-CUm6gmtO.js";import{_ as Y}from"./AutomationWizard.vue_vue_type_script_setup_true_lang-BNke21Sq.js";import{u as q}from"./usePageTitle-BEMSydIZ.js";import{u as v}from"./usePrefectApi-DO-y9p9U.js";import"./mapper-BXnlCs1t.js";import"./api-9onuHMbZ.js";const R=x({__name:"AutomationCreate",async setup(z){let e,u;q("Create Automation");const p=v(),m=y(),l=w(),_=[{text:"Automations",to:m.automations()},{text:"Create"}],{getActions:f,getTrigger:d}=h(),g=([e,u]=T(()=>C()),e=await e,u(),e);async function C(){const o={},[t,a]=await Promise.all([d(),f()]);return t&&(o.trigger=t),a&&(o.actions=a),o}async function A(o){try{await p.automations.createAutomation(o),c(i.success.automationCreate),l.push(m.automations())}catch(t){console.error(t);const a=U(t,i.error.automationCreate);c(a,"error",{timeout:!1})}}return(o,t)=>{const a=V("p-layout-default");return k(),b(a,{class:"automation-create"},{header:s(()=>[r(n(B),{crumbs:_},{actions:s(()=>[r(n(P),{to:n(i).docs.automations},{default:s(()=>t[0]||(t[0]=[N(" Documentation ")])),_:1},8,["to"])]),_:1})]),default:s(()=>[r(Y,{automation:n(g),onSubmit:A},null,8,["automation"])]),_:1})}}});export{R as default};
//# sourceMappingURL=AutomationCreate-BAgbaOyo.js.map
