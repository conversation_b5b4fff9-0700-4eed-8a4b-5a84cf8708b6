import{d as r,k as s,c as n,o as l,l as e,m as o,p as a,d5 as p,d6 as u}from"./index-CUm6gmtO.js";import{u as _}from"./usePageTitle-BEMSydIZ.js";const C=r({__name:"WorkPoolCreate",setup(c){return _("Create Work Pool"),(d,m)=>{const t=s("p-layout-default");return l(),n(t,null,{header:e(()=>[o(a(u))]),default:e(()=>[o(a(p))]),_:1})}}});export{C as default};
//# sourceMappingURL=WorkPoolCreate-DPnNeTdz.js.map
