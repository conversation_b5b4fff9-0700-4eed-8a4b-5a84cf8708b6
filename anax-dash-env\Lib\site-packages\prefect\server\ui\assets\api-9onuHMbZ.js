var et=Object.defineProperty;var tt=(e,t,n)=>t in e?et(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var N=(e,t,n)=>tt(e,typeof t!="symbol"?t+"":t,n);import{cD as nt,cE as ke,cF as rt,cG as st,cH as ot,cI as ne,cJ as it}from"./index-CUm6gmtO.js";import{m as _}from"./mapper-BXnlCs1t.js";function Ne(e,t){return function(){return e.apply(t,arguments)}}const{toString:at}=Object.prototype,{getPrototypeOf:de}=Object,G=(e=>t=>{const n=at.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),x=e=>(e=e.toLowerCase(),t=>G(t)===e),Q=e=>t=>typeof t===e,{isArray:j}=Array,M=Q("undefined");function ct(e){return e!==null&&!M(e)&&e.constructor!==null&&!M(e.constructor)&&C(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Fe=x("ArrayBuffer");function ut(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Fe(e.buffer),t}const lt=Q("string"),C=Q("function"),_e=Q("number"),Z=e=>e!==null&&typeof e=="object",ft=e=>e===!0||e===!1,v=e=>{if(G(e)!=="object")return!1;const t=de(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},dt=x("Date"),ht=x("File"),pt=x("Blob"),mt=x("FileList"),yt=e=>Z(e)&&C(e.pipe),wt=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||C(e.append)&&((t=G(e))==="formdata"||t==="object"&&C(e.toString)&&e.toString()==="[object FormData]"))},bt=x("URLSearchParams"),[gt,Rt,Et,St]=["ReadableStream","Request","Response","Headers"].map(x),Tt=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function $(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),j(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let c;for(r=0;r<i;r++)c=o[r],t.call(null,e[c],c,e)}}function Ue(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const B=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Be=e=>!M(e)&&e!==B;function ie(){const{caseless:e}=Be(this)&&this||{},t={},n=(r,s)=>{const o=e&&Ue(t,s)||s;v(t[o])&&v(r)?t[o]=ie(t[o],r):v(r)?t[o]=ie({},r):j(r)?t[o]=r.slice():t[o]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&$(arguments[r],n);return t}const At=(e,t,n,{allOwnKeys:r}={})=>($(t,(s,o)=>{n&&C(s)?e[o]=Ne(s,n):e[o]=s},{allOwnKeys:r}),e),Ot=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Ct=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},xt=(e,t,n,r)=>{let s,o,i;const c={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)i=s[o],(!r||r(i,e,t))&&!c[i]&&(t[i]=e[i],c[i]=!0);e=n!==!1&&de(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},Pt=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},kt=e=>{if(!e)return null;if(j(e))return e;let t=e.length;if(!_e(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},Nt=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&de(Uint8Array)),Ft=(e,t)=>{const r=(e&&e[Symbol.iterator]).call(e);let s;for(;(s=r.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},_t=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Ut=x("HTMLFormElement"),Bt=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),ye=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Lt=x("RegExp"),Le=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};$(n,(s,o)=>{let i;(i=t(s,o,e))!==!1&&(r[o]=i||s)}),Object.defineProperties(e,r)},Dt=e=>{Le(e,(t,n)=>{if(C(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(C(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},qt=(e,t)=>{const n={},r=s=>{s.forEach(o=>{n[o]=!0})};return j(e)?r(e):r(String(e).split(t)),n},jt=()=>{},It=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function Ht(e){return!!(e&&C(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const Mt=e=>{const t=new Array(10),n=(r,s)=>{if(Z(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[s]=r;const o=j(r)?[]:{};return $(r,(i,c)=>{const f=n(i,s+1);!M(f)&&(o[c]=f)}),t[s]=void 0,o}}return r};return n(e,0)},$t=x("AsyncFunction"),zt=e=>e&&(Z(e)||C(e))&&C(e.then)&&C(e.catch),De=((e,t)=>e?setImmediate:t?((n,r)=>(B.addEventListener("message",({source:s,data:o})=>{s===B&&o===n&&r.length&&r.shift()()},!1),s=>{r.push(s),B.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",C(B.postMessage)),Jt=typeof queueMicrotask<"u"?queueMicrotask.bind(B):typeof process<"u"&&process.nextTick||De,a={isArray:j,isArrayBuffer:Fe,isBuffer:ct,isFormData:wt,isArrayBufferView:ut,isString:lt,isNumber:_e,isBoolean:ft,isObject:Z,isPlainObject:v,isReadableStream:gt,isRequest:Rt,isResponse:Et,isHeaders:St,isUndefined:M,isDate:dt,isFile:ht,isBlob:pt,isRegExp:Lt,isFunction:C,isStream:yt,isURLSearchParams:bt,isTypedArray:Nt,isFileList:mt,forEach:$,merge:ie,extend:At,trim:Tt,stripBOM:Ot,inherits:Ct,toFlatObject:xt,kindOf:G,kindOfTest:x,endsWith:Pt,toArray:kt,forEachEntry:Ft,matchAll:_t,isHTMLForm:Ut,hasOwnProperty:ye,hasOwnProp:ye,reduceDescriptors:Le,freezeMethods:Dt,toObjectSet:qt,toCamelCase:Bt,noop:jt,toFiniteNumber:It,findKey:Ue,global:B,isContextDefined:Be,isSpecCompliantForm:Ht,toJSONObject:Mt,isAsyncFn:$t,isThenable:zt,setImmediate:De,asap:Jt};function m(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}a.inherits(m,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:a.toJSONObject(this.config),code:this.code,status:this.status}}});const qe=m.prototype,je={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{je[e]={value:e}});Object.defineProperties(m,je);Object.defineProperty(qe,"isAxiosError",{value:!0});m.from=(e,t,n,r,s,o)=>{const i=Object.create(qe);return a.toFlatObject(e,i,function(f){return f!==Error.prototype},c=>c!=="isAxiosError"),m.call(i,e.message,t,n,r,s),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const vt=null;function ae(e){return a.isPlainObject(e)||a.isArray(e)}function Ie(e){return a.endsWith(e,"[]")?e.slice(0,-2):e}function we(e,t,n){return e?e.concat(t).map(function(s,o){return s=Ie(s),!n&&o?"["+s+"]":s}).join(n?".":""):t}function Vt(e){return a.isArray(e)&&!e.some(ae)}const Wt=a.toFlatObject(a,{},null,function(t){return/^is[A-Z]/.test(t)});function Y(e,t,n){if(!a.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=a.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,p){return!a.isUndefined(p[y])});const r=n.metaTokens,s=n.visitor||l,o=n.dots,i=n.indexes,f=(n.Blob||typeof Blob<"u"&&Blob)&&a.isSpecCompliantForm(t);if(!a.isFunction(s))throw new TypeError("visitor must be a function");function u(h){if(h===null)return"";if(a.isDate(h))return h.toISOString();if(!f&&a.isBlob(h))throw new m("Blob is not supported. Use a Buffer instead.");return a.isArrayBuffer(h)||a.isTypedArray(h)?f&&typeof Blob=="function"?new Blob([h]):Buffer.from(h):h}function l(h,y,p){let b=h;if(h&&!p&&typeof h=="object"){if(a.endsWith(y,"{}"))y=r?y:y.slice(0,-2),h=JSON.stringify(h);else if(a.isArray(h)&&Vt(h)||(a.isFileList(h)||a.endsWith(y,"[]"))&&(b=a.toArray(h)))return y=Ie(y),b.forEach(function(S,k){!(a.isUndefined(S)||S===null)&&t.append(i===!0?we([y],k,o):i===null?y:y+"[]",u(S))}),!1}return ae(h)?!0:(t.append(we(p,y,o),u(h)),!1)}const d=[],w=Object.assign(Wt,{defaultVisitor:l,convertValue:u,isVisitable:ae});function R(h,y){if(!a.isUndefined(h)){if(d.indexOf(h)!==-1)throw Error("Circular reference detected in "+y.join("."));d.push(h),a.forEach(h,function(b,E){(!(a.isUndefined(b)||b===null)&&s.call(t,b,a.isString(E)?E.trim():E,y,w))===!0&&R(b,y?y.concat(E):[E])}),d.pop()}}if(!a.isObject(e))throw new TypeError("data must be an object");return R(e),t}function be(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function he(e,t){this._pairs=[],e&&Y(e,this,t)}const He=he.prototype;He.append=function(t,n){this._pairs.push([t,n])};He.toString=function(t){const n=t?function(r){return t.call(this,r,be)}:be;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function Kt(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Me(e,t,n){if(!t)return e;const r=n&&n.encode||Kt;a.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let o;if(s?o=s(t,n):o=a.isURLSearchParams(t)?t.toString():new he(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class ge{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){a.forEach(this.handlers,function(r){r!==null&&t(r)})}}const $e={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Xt=typeof URLSearchParams<"u"?URLSearchParams:he,Gt=typeof FormData<"u"?FormData:null,Qt=typeof Blob<"u"?Blob:null,Zt={isBrowser:!0,classes:{URLSearchParams:Xt,FormData:Gt,Blob:Qt},protocols:["http","https","file","blob","url","data"]},pe=typeof window<"u"&&typeof document<"u",ce=typeof navigator=="object"&&navigator||void 0,Yt=pe&&(!ce||["ReactNative","NativeScript","NS"].indexOf(ce.product)<0),en=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",tn=pe&&window.location.href||"http://localhost",nn=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:pe,hasStandardBrowserEnv:Yt,hasStandardBrowserWebWorkerEnv:en,navigator:ce,origin:tn},Symbol.toStringTag,{value:"Module"})),T={...nn,...Zt};function rn(e,t){return Y(e,new T.classes.URLSearchParams,Object.assign({visitor:function(n,r,s,o){return T.isNode&&a.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function sn(e){return a.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function on(e){const t={},n=Object.keys(e);let r;const s=n.length;let o;for(r=0;r<s;r++)o=n[r],t[o]=e[o];return t}function ze(e){function t(n,r,s,o){let i=n[o++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),f=o>=n.length;return i=!i&&a.isArray(s)?s.length:i,f?(a.hasOwnProp(s,i)?s[i]=[s[i],r]:s[i]=r,!c):((!s[i]||!a.isObject(s[i]))&&(s[i]=[]),t(n,r,s[i],o)&&a.isArray(s[i])&&(s[i]=on(s[i])),!c)}if(a.isFormData(e)&&a.isFunction(e.entries)){const n={};return a.forEachEntry(e,(r,s)=>{t(sn(r),s,n,0)}),n}return null}function an(e,t,n){if(a.isString(e))try{return(t||JSON.parse)(e),a.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const z={transitional:$e,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,o=a.isObject(t);if(o&&a.isHTMLForm(t)&&(t=new FormData(t)),a.isFormData(t))return s?JSON.stringify(ze(t)):t;if(a.isArrayBuffer(t)||a.isBuffer(t)||a.isStream(t)||a.isFile(t)||a.isBlob(t)||a.isReadableStream(t))return t;if(a.isArrayBufferView(t))return t.buffer;if(a.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return rn(t,this.formSerializer).toString();if((c=a.isFileList(t))||r.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return Y(c?{"files[]":t}:t,f&&new f,this.formSerializer)}}return o||s?(n.setContentType("application/json",!1),an(t)):t}],transformResponse:[function(t){const n=this.transitional||z.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(a.isResponse(t)||a.isReadableStream(t))return t;if(t&&a.isString(t)&&(r&&!this.responseType||s)){const i=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(c){if(i)throw c.name==="SyntaxError"?m.from(c,m.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:T.classes.FormData,Blob:T.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};a.forEach(["delete","get","head","post","put","patch"],e=>{z.headers[e]={}});const cn=a.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),un=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(i){s=i.indexOf(":"),n=i.substring(0,s).trim().toLowerCase(),r=i.substring(s+1).trim(),!(!n||t[n]&&cn[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Re=Symbol("internals");function H(e){return e&&String(e).trim().toLowerCase()}function V(e){return e===!1||e==null?e:a.isArray(e)?e.map(V):String(e)}function ln(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const fn=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function re(e,t,n,r,s){if(a.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!a.isString(t)){if(a.isString(r))return t.indexOf(r)!==-1;if(a.isRegExp(r))return r.test(t)}}function dn(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function hn(e,t){const n=a.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,o,i){return this[r].call(this,t,s,o,i)},configurable:!0})})}let O=class{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function o(c,f,u){const l=H(f);if(!l)throw new Error("header name must be a non-empty string");const d=a.findKey(s,l);(!d||s[d]===void 0||u===!0||u===void 0&&s[d]!==!1)&&(s[d||f]=V(c))}const i=(c,f)=>a.forEach(c,(u,l)=>o(u,l,f));if(a.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(a.isString(t)&&(t=t.trim())&&!fn(t))i(un(t),n);else if(a.isHeaders(t))for(const[c,f]of t.entries())o(f,c,r);else t!=null&&o(n,t,r);return this}get(t,n){if(t=H(t),t){const r=a.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return ln(s);if(a.isFunction(n))return n.call(this,s,r);if(a.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=H(t),t){const r=a.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||re(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function o(i){if(i=H(i),i){const c=a.findKey(r,i);c&&(!n||re(r,r[c],c,n))&&(delete r[c],s=!0)}}return a.isArray(t)?t.forEach(o):o(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const o=n[r];(!t||re(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const n=this,r={};return a.forEach(this,(s,o)=>{const i=a.findKey(r,o);if(i){n[i]=V(s),delete n[o];return}const c=t?dn(o):String(o).trim();c!==o&&delete n[o],n[c]=V(s),r[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return a.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&a.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[Re]=this[Re]={accessors:{}}).accessors,s=this.prototype;function o(i){const c=H(i);r[c]||(hn(s,i),r[c]=!0)}return a.isArray(t)?t.forEach(o):o(t),this}};O.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);a.reduceDescriptors(O.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});a.freezeMethods(O);function se(e,t){const n=this||z,r=t||n,s=O.from(r.headers);let o=r.data;return a.forEach(e,function(c){o=c.call(n,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function Je(e){return!!(e&&e.__CANCEL__)}function I(e,t,n){m.call(this,e??"canceled",m.ERR_CANCELED,t,n),this.name="CanceledError"}a.inherits(I,m,{__CANCEL__:!0});function ve(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new m("Request failed with status code "+n.status,[m.ERR_BAD_REQUEST,m.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function pn(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function mn(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,o=0,i;return t=t!==void 0?t:1e3,function(f){const u=Date.now(),l=r[o];i||(i=u),n[s]=f,r[s]=u;let d=o,w=0;for(;d!==s;)w+=n[d++],d=d%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),u-i<t)return;const R=l&&u-l;return R?Math.round(w*1e3/R):void 0}}function yn(e,t){let n=0,r=1e3/t,s,o;const i=(u,l=Date.now())=>{n=l,s=null,o&&(clearTimeout(o),o=null),e.apply(null,u)};return[(...u)=>{const l=Date.now(),d=l-n;d>=r?i(u,l):(s=u,o||(o=setTimeout(()=>{o=null,i(s)},r-d)))},()=>s&&i(s)]}const K=(e,t,n=3)=>{let r=0;const s=mn(50,250);return yn(o=>{const i=o.loaded,c=o.lengthComputable?o.total:void 0,f=i-r,u=s(f),l=i<=c;r=i;const d={loaded:i,total:c,progress:c?i/c:void 0,bytes:f,rate:u||void 0,estimated:u&&c&&l?(c-i)/u:void 0,event:o,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(d)},n)},Ee=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Se=e=>(...t)=>a.asap(()=>e(...t)),wn=T.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,T.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(T.origin),T.navigator&&/(msie|trident)/i.test(T.navigator.userAgent)):()=>!0,bn=T.hasStandardBrowserEnv?{write(e,t,n,r,s,o){const i=[e+"="+encodeURIComponent(t)];a.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),a.isString(r)&&i.push("path="+r),a.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function gn(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Rn(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Ve(e,t,n){let r=!gn(t);return e&&r||n==!1?Rn(e,t):t}const Te=e=>e instanceof O?{...e}:e;function q(e,t){t=t||{};const n={};function r(u,l,d,w){return a.isPlainObject(u)&&a.isPlainObject(l)?a.merge.call({caseless:w},u,l):a.isPlainObject(l)?a.merge({},l):a.isArray(l)?l.slice():l}function s(u,l,d,w){if(a.isUndefined(l)){if(!a.isUndefined(u))return r(void 0,u,d,w)}else return r(u,l,d,w)}function o(u,l){if(!a.isUndefined(l))return r(void 0,l)}function i(u,l){if(a.isUndefined(l)){if(!a.isUndefined(u))return r(void 0,u)}else return r(void 0,l)}function c(u,l,d){if(d in t)return r(u,l);if(d in e)return r(void 0,u)}const f={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(u,l,d)=>s(Te(u),Te(l),d,!0)};return a.forEach(Object.keys(Object.assign({},e,t)),function(l){const d=f[l]||s,w=d(e[l],t[l],l);a.isUndefined(w)&&d!==c||(n[l]=w)}),n}const We=e=>{const t=q({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:c}=t;t.headers=i=O.from(i),t.url=Me(Ve(t.baseURL,t.url),e.params,e.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let f;if(a.isFormData(n)){if(T.hasStandardBrowserEnv||T.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((f=i.getContentType())!==!1){const[u,...l]=f?f.split(";").map(d=>d.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...l].join("; "))}}if(T.hasStandardBrowserEnv&&(r&&a.isFunction(r)&&(r=r(t)),r||r!==!1&&wn(t.url))){const u=s&&o&&bn.read(o);u&&i.set(s,u)}return t},En=typeof XMLHttpRequest<"u",Sn=En&&function(e){return new Promise(function(n,r){const s=We(e);let o=s.data;const i=O.from(s.headers).normalize();let{responseType:c,onUploadProgress:f,onDownloadProgress:u}=s,l,d,w,R,h;function y(){R&&R(),h&&h(),s.cancelToken&&s.cancelToken.unsubscribe(l),s.signal&&s.signal.removeEventListener("abort",l)}let p=new XMLHttpRequest;p.open(s.method.toUpperCase(),s.url,!0),p.timeout=s.timeout;function b(){if(!p)return;const S=O.from("getAllResponseHeaders"in p&&p.getAllResponseHeaders()),A={data:!c||c==="text"||c==="json"?p.responseText:p.response,status:p.status,statusText:p.statusText,headers:S,config:e,request:p};ve(function(U){n(U),y()},function(U){r(U),y()},A),p=null}"onloadend"in p?p.onloadend=b:p.onreadystatechange=function(){!p||p.readyState!==4||p.status===0&&!(p.responseURL&&p.responseURL.indexOf("file:")===0)||setTimeout(b)},p.onabort=function(){p&&(r(new m("Request aborted",m.ECONNABORTED,e,p)),p=null)},p.onerror=function(){r(new m("Network Error",m.ERR_NETWORK,e,p)),p=null},p.ontimeout=function(){let k=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const A=s.transitional||$e;s.timeoutErrorMessage&&(k=s.timeoutErrorMessage),r(new m(k,A.clarifyTimeoutError?m.ETIMEDOUT:m.ECONNABORTED,e,p)),p=null},o===void 0&&i.setContentType(null),"setRequestHeader"in p&&a.forEach(i.toJSON(),function(k,A){p.setRequestHeader(A,k)}),a.isUndefined(s.withCredentials)||(p.withCredentials=!!s.withCredentials),c&&c!=="json"&&(p.responseType=s.responseType),u&&([w,h]=K(u,!0),p.addEventListener("progress",w)),f&&p.upload&&([d,R]=K(f),p.upload.addEventListener("progress",d),p.upload.addEventListener("loadend",R)),(s.cancelToken||s.signal)&&(l=S=>{p&&(r(!S||S.type?new I(null,e,p):S),p.abort(),p=null)},s.cancelToken&&s.cancelToken.subscribe(l),s.signal&&(s.signal.aborted?l():s.signal.addEventListener("abort",l)));const E=pn(s.url);if(E&&T.protocols.indexOf(E)===-1){r(new m("Unsupported protocol "+E+":",m.ERR_BAD_REQUEST,e));return}p.send(o||null)})},Tn=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,s;const o=function(u){if(!s){s=!0,c();const l=u instanceof Error?u:this.reason;r.abort(l instanceof m?l:new I(l instanceof Error?l.message:l))}};let i=t&&setTimeout(()=>{i=null,o(new m(`timeout ${t} of ms exceeded`,m.ETIMEDOUT))},t);const c=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:f}=r;return f.unsubscribe=()=>a.asap(c),f}},An=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,s;for(;r<n;)s=r+t,yield e.slice(r,s),r=s},On=async function*(e,t){for await(const n of Cn(e))yield*An(n,t)},Cn=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Ae=(e,t,n,r)=>{const s=On(e,t);let o=0,i,c=f=>{i||(i=!0,r&&r(f))};return new ReadableStream({async pull(f){try{const{done:u,value:l}=await s.next();if(u){c(),f.close();return}let d=l.byteLength;if(n){let w=o+=d;n(w)}f.enqueue(new Uint8Array(l))}catch(u){throw c(u),u}},cancel(f){return c(f),s.return()}},{highWaterMark:2})},ee=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Ke=ee&&typeof ReadableStream=="function",xn=ee&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Xe=(e,...t)=>{try{return!!e(...t)}catch{return!1}},Pn=Ke&&Xe(()=>{let e=!1;const t=new Request(T.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Oe=64*1024,ue=Ke&&Xe(()=>a.isReadableStream(new Response("").body)),X={stream:ue&&(e=>e.body)};ee&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!X[t]&&(X[t]=a.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new m(`Response type '${t}' is not supported`,m.ERR_NOT_SUPPORT,r)})})})(new Response);const kn=async e=>{if(e==null)return 0;if(a.isBlob(e))return e.size;if(a.isSpecCompliantForm(e))return(await new Request(T.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(a.isArrayBufferView(e)||a.isArrayBuffer(e))return e.byteLength;if(a.isURLSearchParams(e)&&(e=e+""),a.isString(e))return(await xn(e)).byteLength},Nn=async(e,t)=>{const n=a.toFiniteNumber(e.getContentLength());return n??kn(t)},Fn=ee&&(async e=>{let{url:t,method:n,data:r,signal:s,cancelToken:o,timeout:i,onDownloadProgress:c,onUploadProgress:f,responseType:u,headers:l,withCredentials:d="same-origin",fetchOptions:w}=We(e);u=u?(u+"").toLowerCase():"text";let R=Tn([s,o&&o.toAbortSignal()],i),h;const y=R&&R.unsubscribe&&(()=>{R.unsubscribe()});let p;try{if(f&&Pn&&n!=="get"&&n!=="head"&&(p=await Nn(l,r))!==0){let A=new Request(t,{method:"POST",body:r,duplex:"half"}),F;if(a.isFormData(r)&&(F=A.headers.get("content-type"))&&l.setContentType(F),A.body){const[U,J]=Ee(p,K(Se(f)));r=Ae(A.body,Oe,U,J)}}a.isString(d)||(d=d?"include":"omit");const b="credentials"in Request.prototype;h=new Request(t,{...w,signal:R,method:n.toUpperCase(),headers:l.normalize().toJSON(),body:r,duplex:"half",credentials:b?d:void 0});let E=await fetch(h);const S=ue&&(u==="stream"||u==="response");if(ue&&(c||S&&y)){const A={};["status","statusText","headers"].forEach(me=>{A[me]=E[me]});const F=a.toFiniteNumber(E.headers.get("content-length")),[U,J]=c&&Ee(F,K(Se(c),!0))||[];E=new Response(Ae(E.body,Oe,U,()=>{J&&J(),y&&y()}),A)}u=u||"text";let k=await X[a.findKey(X,u)||"text"](E,e);return!S&&y&&y(),await new Promise((A,F)=>{ve(A,F,{data:k,headers:O.from(E.headers),status:E.status,statusText:E.statusText,config:e,request:h})})}catch(b){throw y&&y(),b&&b.name==="TypeError"&&/fetch/i.test(b.message)?Object.assign(new m("Network Error",m.ERR_NETWORK,e,h),{cause:b.cause||b}):m.from(b,b&&b.code,e,h)}}),le={http:vt,xhr:Sn,fetch:Fn};a.forEach(le,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Ce=e=>`- ${e}`,_n=e=>a.isFunction(e)||e===null||e===!1,Ge={getAdapter:e=>{e=a.isArray(e)?e:[e];const{length:t}=e;let n,r;const s={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!_n(n)&&(r=le[(i=String(n)).toLowerCase()],r===void 0))throw new m(`Unknown adapter '${i}'`);if(r)break;s[i||"#"+o]=r}if(!r){const o=Object.entries(s).map(([c,f])=>`adapter ${c} `+(f===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Ce).join(`
`):" "+Ce(o[0]):"as no adapter specified";throw new m("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:le};function oe(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new I(null,e)}function xe(e){return oe(e),e.headers=O.from(e.headers),e.data=se.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Ge.getAdapter(e.adapter||z.adapter)(e).then(function(r){return oe(e),r.data=se.call(e,e.transformResponse,r),r.headers=O.from(r.headers),r},function(r){return Je(r)||(oe(e),r&&r.response&&(r.response.data=se.call(e,e.transformResponse,r.response),r.response.headers=O.from(r.response.headers))),Promise.reject(r)})}const Qe="1.8.2",te={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{te[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Pe={};te.transitional=function(t,n,r){function s(o,i){return"[Axios v"+Qe+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,c)=>{if(t===!1)throw new m(s(i," has been removed"+(n?" in "+n:"")),m.ERR_DEPRECATED);return n&&!Pe[i]&&(Pe[i]=!0,console.warn(s(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,c):!0}};te.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function Un(e,t,n){if(typeof e!="object")throw new m("options must be an object",m.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const o=r[s],i=t[o];if(i){const c=e[o],f=c===void 0||i(c,o,e);if(f!==!0)throw new m("option "+o+" must be "+f,m.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new m("Unknown option "+o,m.ERR_BAD_OPTION)}}const W={assertOptions:Un,validators:te},P=W.validators;let L=class{constructor(t){this.defaults=t,this.interceptors={request:new ge,response:new ge}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=q(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:o}=n;r!==void 0&&W.assertOptions(r,{silentJSONParsing:P.transitional(P.boolean),forcedJSONParsing:P.transitional(P.boolean),clarifyTimeoutError:P.transitional(P.boolean)},!1),s!=null&&(a.isFunction(s)?n.paramsSerializer={serialize:s}:W.assertOptions(s,{encode:P.function,serialize:P.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),W.assertOptions(n,{baseUrl:P.spelling("baseURL"),withXsrfToken:P.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&a.merge(o.common,o[n.method]);o&&a.forEach(["delete","get","head","post","put","patch","common"],h=>{delete o[h]}),n.headers=O.concat(i,o);const c=[];let f=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(n)===!1||(f=f&&y.synchronous,c.unshift(y.fulfilled,y.rejected))});const u=[];this.interceptors.response.forEach(function(y){u.push(y.fulfilled,y.rejected)});let l,d=0,w;if(!f){const h=[xe.bind(this),void 0];for(h.unshift.apply(h,c),h.push.apply(h,u),w=h.length,l=Promise.resolve(n);d<w;)l=l.then(h[d++],h[d++]);return l}w=c.length;let R=n;for(d=0;d<w;){const h=c[d++],y=c[d++];try{R=h(R)}catch(p){y.call(this,p);break}}try{l=xe.call(this,R)}catch(h){return Promise.reject(h)}for(d=0,w=u.length;d<w;)l=l.then(u[d++],u[d++]);return l}getUri(t){t=q(this.defaults,t);const n=Ve(t.baseURL,t.url,t.allowAbsoluteUrls);return Me(n,t.params,t.paramsSerializer)}};a.forEach(["delete","get","head","options"],function(t){L.prototype[t]=function(n,r){return this.request(q(r||{},{method:t,url:n,data:(r||{}).data}))}});a.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,c){return this.request(q(c||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}L.prototype[t]=n(),L.prototype[t+"Form"]=n(!0)});let Bn=class Ze{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(s=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](s);r._listeners=null}),this.promise.then=s=>{let o;const i=new Promise(c=>{r.subscribe(c),o=c}).then(s);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,c){r.reason||(r.reason=new I(o,i,c),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Ze(function(s){t=s}),cancel:t}}};function Ln(e){return function(n){return e.apply(null,n)}}function Dn(e){return a.isObject(e)&&e.isAxiosError===!0}const fe={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(fe).forEach(([e,t])=>{fe[t]=e});function Ye(e){const t=new L(e),n=Ne(L.prototype.request,t);return a.extend(n,L.prototype,t,{allOwnKeys:!0}),a.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return Ye(q(e,s))},n}const g=Ye(z);g.Axios=L;g.CanceledError=I;g.CancelToken=Bn;g.isCancel=Je;g.VERSION=Qe;g.toFormData=Y;g.AxiosError=m;g.Cancel=g.CanceledError;g.all=function(t){return Promise.all(t)};g.spread=Ln;g.isAxiosError=Dn;g.mergeConfig=q;g.AxiosHeaders=O;g.formToJSON=e=>ze(a.isHTMLForm(e)?new FormData(e):e);g.getAdapter=Ge.getAdapter;g.HttpStatusCode=fe;g.default=g;const{Axios:Xn,AxiosError:Gn,CanceledError:Qn,isCancel:Zn,CancelToken:Yn,VERSION:er,all:tr,Cancel:nr,isAxiosError:qn,spread:rr,toFormData:sr,AxiosHeaders:or,HttpStatusCode:ir,formToJSON:ar,getAdapter:cr,mergeConfig:ur}=g;class D{static async load(){if(this.settings!==null)return this.settings;if(this.promise!==null)return this.promise;this.promise=new Promise(n=>g.get("/ui-settings",{baseURL:this.baseUrl}).then(({data:r})=>_.map("SettingsResponse",r,"Settings")).then(n));const t=await this.promise;return this.settings=t}static async get(t,n){var s;await this.load();const r=(s=this.settings)==null?void 0:s[t];if(r===void 0){if(n)return n;throw`UI setting "${t}" does not exist and no default was provided.`}return r}}N(D,"settings",null),N(D,"promise",null),N(D,"baseUrl",nt());const lr={getFeatureFlags:()=>D.get("flags")};class jn extends ke{constructor(){super(...arguments);N(this,"routePrefix","/admin")}getSettings(){return this.get("/settings").then(({data:n})=>n)}async getVersion(){return await this.get("/version").then(({data:n})=>n)}async authCheck(){if(!await D.get("auth"))return 200;try{return(await this.get("/version")).status}catch(r){return r.response?r.response.status:500}}}class In extends rt{async getAutomation(t){const{data:n}=await this.get(`/${t}`);return _.map("AutomationResponse",n,"Automation")}async getAutomations(t={}){const{data:n}=await this.post("/filter",t);return _.map("AutomationResponse",n,"Automation")}async createAutomation(t){const n=_.map("AutomationCreate",t,"AutomationCreateRequest"),{data:r}=await this.post("/",n);return _.map("AutomationResponse",r,"Automation")}updateAutomation(t,n){const r=_.map("AutomationCreate",n,"AutomationCreateRequest");return this.put(`/${t}`,r)}async getResourceAutomations(t){const{data:n}=await this.get(`related-to/${t}`);return _.map("AutomationResponse",n,"Automation")}}const Hn=1;class Mn extends ke{constructor(n,r=null){super(n,r);N(this,"csrfToken");N(this,"clientId",st());N(this,"refreshTimeout",null);N(this,"ongoingRefresh",null);this.startBackgroundTokenRefresh()}async addCsrfHeaders(n){if(!await D.get("csrfEnabled"))return;const s=await this.getCsrfToken();n.headers["Prefect-Csrf-Token"]=s.token,n.headers["Prefect-Csrf-Client"]=this.clientId,n.headers["Prefect-Csrf-Retry-Count"]=n.headers["Prefect-Csrf-Retry-Count"]??"0"}async getCsrfToken(){if(this.shouldRefreshToken()&&await this.refreshCsrfToken(),!this.csrfToken)throw new Error("CSRF token not available");return this.csrfToken}refreshCsrfToken(n=!1){if(!n&&!this.shouldRefreshToken())return this.ongoingRefresh??Promise.resolve();if(this.ongoingRefresh)return this.ongoingRefresh;const r=async()=>{try{const s=localStorage.getItem("prefect-password"),o=await this.get(`/csrf-token?client=${this.clientId}`,{headers:s?{Authorization:`Basic ${s}`}:void 0});this.csrfToken=_.map("CsrfTokenResponse",o.data,"CsrfToken"),this.ongoingRefresh=null}catch(s){throw this.ongoingRefresh=null,s}};return this.ongoingRefresh=r(),this.ongoingRefresh}shouldRefreshToken(){return this.csrfToken?new Date>this.csrfToken.expiration:!0}async startBackgroundTokenRefresh(){if(!await D.get("csrfEnabled"))return;const r=()=>{if(this.csrfToken){const o=new Date,i=new Date(this.csrfToken.expiration),c=this.csrfToken.issued,f=i.getTime()-c.getTime();return c.getTime()+f*.75-o.getTime()}return 0},s=async()=>{await this.refreshCsrfToken(!0),this.refreshTimeout=setTimeout(s,r())};this.refreshTimeout=setTimeout(s,r())}}function $n(e){return ot(e)?e.response.status===403&&e.response.data.detail.includes("Invalid CSRF token"):!1}function zn(e,t){t.interceptors.request.use(async n=>{var o;const r=(o=n.method)==null?void 0:o.toLowerCase();r&&["post","patch","put","delete"].includes(r)&&await e.addCsrfHeaders(n);const s=localStorage.getItem("prefect-password");return s&&(n.headers.Authorization=`Basic ${s}`),n}),t.interceptors.response.use(void 0,async n=>{if(qn(n)&&$n(n)){const{config:r}=n;if(r!=null&&r.headers["Prefect-Csrf-Retry-Count"]){const s=parseInt(r.headers["Prefect-Csrf-Retry-Count"],10);if(s<Hn)return await e.addCsrfHeaders(r),r.headers["Prefect-Csrf-Retry-Count"]=(s+1).toString(),t(r)}}return Promise.reject(n)})}function fr(e){const t=ne(new Mn(e));function n(s){zn(t,s);const o=localStorage.getItem("prefect-password");o&&(s.defaults.headers.common.Authorization=`Basic ${o}`)}return{...it(e,n),csrf:t,admin:ne(new jn(e,n)),automations:ne(new In(e,n))}}const dr=Symbol("PrefectApi");export{D as U,fr as c,dr as p,lr as u};
//# sourceMappingURL=api-9onuHMbZ.js.map
