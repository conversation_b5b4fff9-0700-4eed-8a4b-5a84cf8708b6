import{d as T,h as F,u as x,al as O,W as P,j as o,bF as m,k as w,c as r,a as u,o as n,l as p,p as k,cn as A,co as D,_ as N,cp as V,m as W,cq as j,K as y,cr as q,be as E}from"./index-CUm6gmtO.js";import{u as H}from"./usePageTitle-BEMSydIZ.js";const Z=T({__name:"BlocksCatalogCreate",setup(K){const l=F(),t=x(),i=O("redirect"),b=P("blockTypeSlug"),d=o(()=>b.value?[b.value]:null),f=m(l.blockTypes.getBlockTypeBySlug,d),e=o(()=>f.response),v=o(()=>e.value?[e.value.id]:null),h=m(l.blockSchemas.getBlockSchemaForBlockType,v),s=o(()=>h.response);function _(a){l.blockDocuments.createBlockDocument(a).then(({id:c})=>g(c)).catch(c=>{y("Failed to create block","error"),console.error(c)})}function S(){t.back()}function g(a){if(y("Block created successfully","success"),i.value){const c=t.resolve(q(i.value));t.push(c);return}t.push(E.block(a))}const B=o(()=>e.value?`Create ${e.value.name} Block`:"Create Block");return H(B),(a,c)=>{const C=w("p-layout-default");return e.value?(n(),r(C,{key:0,class:"blocks-catalog-create"},{header:p(()=>[W(k(j),{"block-type":e.value},null,8,["block-type"])]),default:p(()=>[e.value?(n(),r(k(A),{key:0,"block-type":e.value},{default:p(()=>[s.value?(n(),r(k(D),N({key:s.value.id,"block-schema":s.value},V({submit:_,cancel:S})),null,16,["block-schema"])):u("",!0)]),_:1},8,["block-type"])):u("",!0)]),_:1})):u("",!0)}}});export{Z as default};
//# sourceMappingURL=BlocksCatalogCreate-6VSm5aRl.js.map
