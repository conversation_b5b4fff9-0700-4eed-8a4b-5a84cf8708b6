{"version": 3, "file": "ConcurrencyLimits-Bb9sGeUS.js", "sources": ["../../src/pages/ConcurrencyLimits.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"concurrency-limits\">\n    <template #header>\n      <PageHeading :crumbs=\"[{ text: 'Concurrency' }]\" />\n    </template>\n    <p-tabs v-model:selected=\"tab\" :tabs=\"tabs\">\n      <template #global>\n        <PageHeading size=\"lg\" :crumbs=\"[{ text: 'Global Concurrency Limits' }]\">\n          <template #after-crumbs>\n            <p-button small icon=\"PlusIcon\" @click=\"openGlobal\" />\n          </template>\n        </PageHeading>\n        <ConcurrencyLimitsV2CreateModal v-model:showModal=\"showModalGlobal\" />\n        <ConcurrencyLimitsV2Table class=\"concurrency-limits__global-table\" />\n      </template>\n      <template #task-run>\n        <PageHeading size=\"lg\" :crumbs=\"[{ text: 'Task Run Concurrency Limits' }]\">\n          <template #after-crumbs>\n            <p-button small icon=\"PlusIcon\" @click=\"openTaskRun\" />\n          </template>\n        </PageHeading>\n        <ConcurrencyLimitsCreateModal v-model:showModal=\"showModalTaskRun\" />\n        <ConcurrencyLimitsTable class=\"concurrency-limits__task-limits-table\" />\n      </template>\n    </p-tabs>\n  </p-layout-default>\n</template>\n\n  <script lang=\"ts\" setup>\n  import { PageHeading, ConcurrencyLimitsV2Table, ConcurrencyLimitsTable, ConcurrencyLimitsCreateModal, ConcurrencyLimitsV2CreateModal, useShowModal } from '@prefecthq/prefect-ui-library'\n  import { useRouteQueryParam } from '@prefecthq/vue-compositions'\n\n  const { showModal: showModalGlobal, open: openGlobal } = useShowModal()\n  const { showModal: showModalTaskRun, open: openTaskRun } = useShowModal()\n\n  const tabs = [\n    { label: 'Global' },\n    { label: 'Task Run' },\n  ]\n\n  const tab = useRouteQueryParam('tab', 'Global')\n</script>\n\n<style>\n.concurrency-limits__global-table { @apply\n  mb-2\n  mt-4\n}\n\n.concurrency-limits__task-limits-table { @apply\n  mb-2\n  mt-4\n}\n</style>"], "names": ["showModalGlobal", "openGlobal", "useShowModal", "showModalTaskRun", "openTaskRun", "tabs", "tab", "useRouteQueryParam"], "mappings": "gMAgCE,KAAM,CAAE,UAAWA,EAAiB,KAAMC,CAAA,EAAeC,EAAa,EAChE,CAAE,UAAWC,EAAkB,KAAMC,CAAA,EAAgBF,EAAa,EAElEG,EAAO,CACX,CAAE,MAAO,QAAS,EAClB,CAAE,MAAO,UAAW,CACtB,EAEMC,EAAMC,EAAmB,MAAO,QAAQ"}