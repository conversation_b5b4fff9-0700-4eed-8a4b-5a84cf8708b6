import{d as A,L as D,ae as M,S as F,k as c,c as v,o as f,l as o,m as t,B as b,n as S,a as h,t as T,p as e,v as I,K as $,dg as L,dh as W,j as x,cR as G,di as K,dj as q,dk as l,be as i,G as H,dl as Q,i as Y,dm as z,dn as X,I as Z,c7 as J,aI as V,bw as ee,dp as te,dq as oe,dr as ne,r as se,aw as ae,ds as le,aR as C,dt as ie,du as re,dv as ue,dw as ce}from"./index-CUm6gmtO.js";import{u as me,c as pe}from"./useCan-BactzY1Z.js";import{U as de,u as _e,c as fe,p as ve}from"./api-9onuHMbZ.js";import"./mapper-BXnlCs1t.js";const ge={class:"flex gap-x-2 items-center"},ke={key:0,class:"text-sm italic"},we="http://prefect.io/slack?utm_source=oss&utm_medium=oss&utm_campaign=oss_popup&utm_term=none&utm_content=none",B="join-the-community-modal",ye="https://getform.io/f/eapderva",be=A({__name:"JoinTheCommunityModal",props:{showModal:{type:Boolean},showModalModifiers:{}},emits:["update:showModal"],setup(a){const s=D(a,"showModal"),u=M(!1),r=M(),{validate:k,state:m}=F(r,[L("Email"),W("Email")]),g=M(!1),d=M("");async function _(){if(await k()){d.value="",g.value=!0;try{await fetch(ye,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:r.value}),redirect:"manual"}),s.value=!1,$("Successfully subscribed","success")}catch(w){d.value="An error occurred. Please try again.",console.error(w)}finally{g.value=!1}}}return(w,n)=>{const p=c("p-button"),P=c("p-divider"),U=c("p-text-input"),j=c("p-label"),E=c("p-form"),O=c("p-message"),R=c("p-modal");return f(),v(R,{"show-modal":s.value,"onUpdate:showModal":n[2]||(n[2]=y=>s.value=y),title:"Join the Prefect Community"},{header:o(()=>n[3]||(n[3]=[S("h2",null,"Join the Community",-1)])),default:o(()=>[n[5]||(n[5]=S("p",null," Connect with 25k+ engineers scaling Python with Prefect. Show us your work and be the first to know about new Prefect features. ",-1)),S("div",ge,[t(p,{primary:"",icon:"Slack",to:we,target:"_blank",onClick:n[0]||(n[0]=y=>u.value=!0)},{default:o(()=>n[4]||(n[4]=[b(" Join us on Slack ")])),_:1}),u.value?(f(),T("span",ke," Thanks for joining our community! ")):h("",!0)]),t(P,{class:"-my-3"}),t(E,{id:B,onSubmit:_},{default:o(()=>[t(j,{label:"Notify me about Prefect updates",state:e(m),message:e(m).error},{default:o(({id:y})=>[t(U,{id:y,modelValue:r.value,"onUpdate:modelValue":n[1]||(n[1]=N=>r.value=N),placeholder:"<EMAIL>",state:e(m)},null,8,["id","modelValue","state"])]),_:1},8,["state","message"])]),_:1}),d.value?(f(),v(O,{key:0,error:""},{default:o(()=>[b(I(d.value),1)]),_:1})):h("",!0)]),cancel:o(y=>[t(p,{class:"sm:order-first",onClick:y.close},{default:o(()=>n[6]||(n[6]=[b(" Skip ")])),_:2},1032,["onClick"])]),actions:o(()=>[t(p,{primary:"",type:"submit",form:B,loading:g.value},{default:o(()=>n[7]||(n[7]=[b(" Sign up ")])),_:1},8,["loading"])]),_:1},8,["show-modal"])}}}),he={href:"https://www.prefect.io/cloud-vs-oss?utm_source=oss&utm_medium=oss&utm_campaign=oss&utm_term=none&utm_content=none",target:"_blank"},Ce=A({__name:"ContextSidebar",setup(a){const s=me(),u=x(()=>s.read.work_pool),{showModal:r,open:k}=G(),{value:m}=K("local","join-the-community-modal-dismissed",!1);function g(d){r.value=d,d||(m.value=!0)}return(d,_)=>{const w=c("p-icon"),n=c("router-link"),p=c("p-button");return f(),v(e(q),{class:"context-sidebar"},{header:o(()=>[t(n,{to:e(i).root(),class:"context-sidebar__logo-link"},{default:o(()=>[t(w,{icon:"Prefect",class:"context-sidebar__logo-icon"})]),_:1},8,["to"])]),footer:o(()=>[S("a",he,[t(e(l),null,{default:o(()=>[_[1]||(_[1]=S("div",null," Ready to scale? ",-1)),t(p,{primary:"",small:"",class:"context-sidebar__upgade-button"},{default:o(()=>_[0]||(_[0]=[b(" Upgrade ")])),_:1})]),_:1})]),t(e(l),{onClick:e(k)},{default:o(()=>[_[2]||(_[2]=b(" Join the Community ")),t(be,{"show-modal":e(r)||!e(m),"onUpdate:showModal":g},null,8,["show-modal"])]),_:1},8,["onClick"]),t(e(l),{title:"Settings",to:e(i).settings()},null,8,["to"])]),default:o(()=>[t(e(l),{title:"Dashboard",to:e(i).dashboard()},null,8,["to"]),t(e(l),{title:"Runs",to:e(i).runs()},null,8,["to"]),t(e(l),{title:"Flows",to:e(i).flows()},null,8,["to"]),t(e(l),{title:"Deployments",to:e(i).deployments()},null,8,["to"]),u.value?(f(),v(e(l),{key:0,title:"Work Pools",to:e(i).workPools()},null,8,["to"])):h("",!0),u.value?h("",!0):(f(),v(e(l),{key:1,title:"Work Queues",to:e(i).workQueues()},null,8,["to"])),t(e(l),{title:"Blocks",to:e(i).blocks()},null,8,["to"]),t(e(l),{title:e(H).info.variables,to:e(i).variables()},null,8,["title","to"]),t(e(l),{title:"Automations",to:e(i).automations()},null,8,["to"]),t(e(l),{title:"Event Feed",to:e(i).events()},null,8,["to"]),t(e(l),{title:"Notifications",to:e(i).notifications()},null,8,["to"]),t(e(l),{title:"Concurrency",to:e(i).concurrencyLimits()},null,8,["to"])]),_:1})}}});async function Me(){const a=await de.get("apiUrl"),s={baseUrl:a};return a.startsWith("/")&&Q(),{config:s}}function Se(){const a=Y(_e.getFeatureFlags,[]),s=x(()=>[...z,...a.response??[]]),u=X(s),r=x(()=>a.loading);return{can:u,pending:r}}function Pe(){const a=M(!1);function s(){a.value=!a.value}function u(){a.value=!0}function r(){a.value=!1}return{mobileMenuOpen:a,open:u,close:r,toggle:s}}const xe={class:"app-router-view"},Ve=A({__name:"AppRouterView",async setup(a){let s,u;const{can:r}=Se(),{config:k}=([s,u]=Z(()=>Me()),s=await s,u(),s),m=fe(k),g=ue();C(pe,r),C(ie,r),C(ve,m),C(re,m),C(ce,g),m.admin.authCheck().then(p=>{p==401?J.currentRoute.value.name!=="login"&&($("Authentication failed.","error",{timeout:!1}),J.push({name:"login",query:{redirect:J.currentRoute.value.fullPath}})):m.health.isHealthy().then(P=>{P||$(`Can't connect to Server API at ${k.baseUrl}. Check that it's accessible from your machine.`,"error",{timeout:!1})})});const{mobileMenuOpen:d,toggle:_,close:w}=Pe(),n=x(()=>V.lg||d.value);return ee(()=>document.body.classList.toggle("body-scrolling-disabled",n.value&&!V.lg)),(p,P)=>{const U=c("router-link"),j=c("p-button");return f(),T("div",xe,[!e(V).lg&&!p.$route.meta.public?(f(),v(e(oe),{key:0,class:"app-router-view__mobile-menu"},{"upper-links":o(()=>[t(U,{to:e(i).root()},{default:o(()=>[t(e(te),{icon:"Prefect",class:"app-router-view__prefect-icon"})]),_:1},8,["to"])]),"bottom-links":o(()=>[t(j,{small:"",icon:"Bars3Icon",class:"app-router-view__menu-icon",onClick:e(_)},null,8,["onClick"])]),_:1})):h("",!0),n.value&&!p.$route.meta.public?(f(),v(Ce,{key:1,class:"app-router-view__sidebar",onClick:e(w)},null,8,["onClick"])):h("",!0),t(e(le),{class:ae(["app-router-view__view",{"app-router-view__view--public":p.$route.meta.public}])},{default:o(({Component:E})=>[t(ne,{name:"app-router-view-fade",mode:"out-in"},{default:o(()=>[(f(),v(se(E)))]),_:2},1024)]),_:1},8,["class"])])}}});export{Ve as default};
//# sourceMappingURL=AppRouterView-WtZ87VFj.js.map
