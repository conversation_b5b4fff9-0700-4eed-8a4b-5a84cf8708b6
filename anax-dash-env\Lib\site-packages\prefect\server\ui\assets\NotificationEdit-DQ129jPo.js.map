{"version": 3, "file": "NotificationEdit-DQ129jPo.js", "sources": ["../../src/pages/NotificationEdit.vue"], "sourcesContent": ["<template>\n  <p-layout-default>\n    <template #header>\n      <PageHeadingNotificationEdit />\n    </template>\n    <NotificationForm v-if=\"notification\" v-model:notification=\"notification\" @submit=\"submit\" @cancel=\"cancel\" />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { showToast } from '@prefecthq/prefect-design'\n  import { NotificationForm, Notification, PageHeadingNotificationEdit, useWorkspaceApi } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam } from '@prefecthq/vue-compositions'\n  import { ref } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import router, { routes } from '@/router'\n\n  const api = useWorkspaceApi()\n  const notificationId = useRouteParam('notificationId')\n  const notification = ref({ ...await api.notifications.getNotification(notificationId.value) })\n\n  async function submit(notification: Partial<Notification>): Promise<void> {\n    try {\n      await api.notifications.updateNotification(notificationId.value, notification)\n      router.push(routes.notifications())\n    } catch (error) {\n      showToast('Error updating notification', 'error')\n      console.warn(error)\n    }\n  }\n\n  function cancel(): void {\n    router.push(routes.notifications())\n  }\n\n  usePageTitle('Edit Notification')\n</script>"], "names": ["api", "useWorkspaceApi", "notificationId", "useRouteParam", "notification", "ref", "__temp", "__restore", "_withAsyncContext", "submit", "router", "routes", "error", "showToast", "cancel", "usePageTitle"], "mappings": "yPAiBE,MAAMA,EAAMC,EAAgB,EACtBC,EAAiBC,EAAc,gBAAgB,EAC/CC,EAAeC,EAAI,CAAE,IAAS,CAAAC,EAAAC,CAAA,EAAAC,EAAA,IAAAR,EAAI,cAAc,gBAAgBE,EAAe,KAAK,CAAA,mBAAG,EAE7F,eAAeO,EAAOL,EAAoD,CACpE,GAAA,CACF,MAAMJ,EAAI,cAAc,mBAAmBE,EAAe,MAAOE,CAAY,EACtEM,EAAA,KAAKC,EAAO,eAAe,QAC3BC,EAAO,CACdC,EAAU,8BAA+B,OAAO,EAChD,QAAQ,KAAKD,CAAK,CAAA,CACpB,CAGF,SAASE,GAAe,CACfJ,EAAA,KAAKC,EAAO,eAAe,CAAA,CAGpC,OAAAI,EAAa,mBAAmB"}