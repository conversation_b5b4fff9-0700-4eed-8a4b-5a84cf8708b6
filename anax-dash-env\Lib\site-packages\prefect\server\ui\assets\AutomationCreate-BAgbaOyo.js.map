{"version": 3, "file": "AutomationCreate-BAgbaOyo.js", "sources": ["../../src/pages/AutomationCreate.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"automation-create\">\n    <template #header>\n      <PageHeading :crumbs>\n        <template #actions>\n          <DocumentationButton :to=\"localization.docs.automations\">\n            Documentation\n          </DocumentationButton>\n        </template>\n      </PageHeading>\n    </template>\n\n    <AutomationWizard :automation @submit=\"submit\" />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { BreadCrumbs, showToast } from '@prefecthq/prefect-design'\n  import { PageHeading, DocumentationButton, getApiErrorMessage, localization, useCreateAutomationQueryParams, useWorkspaceRoutes } from '@prefecthq/prefect-ui-library'\n  import { useRouter } from 'vue-router'\n  import AutomationWizard from '@/components/AutomationWizard.vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { usePrefectApi } from '@/compositions/usePrefectApi'\n  import { Automation } from '@/types/automation'\n\n  usePageTitle('Create Automation')\n\n  const api = usePrefectApi()\n  const routes = useWorkspaceRoutes()\n  const router = useRouter()\n\n  const crumbs: BreadCrumbs = [\n    { text: 'Automations', to: routes.automations() },\n    { text: 'Create' },\n  ]\n\n  const { getActions, getTrigger } = useCreateAutomationQueryParams()\n\n  const automation = await getAutomationTemplate()\n\n  async function getAutomationTemplate(): Promise<Partial<Automation>> {\n    const automation: Partial<Automation> = {}\n\n    const [trigger, actions] = await Promise.all([\n      getTrigger(),\n      getActions(),\n    ])\n\n    if (trigger) {\n      automation.trigger = trigger\n    }\n\n    if (actions) {\n      automation.actions = actions\n    }\n\n    return automation\n  }\n\n  async function submit(automation: Automation): Promise<void> {\n    try {\n      await api.automations.createAutomation(automation)\n\n      showToast(localization.success.automationCreate)\n\n      router.push(routes.automations())\n    } catch (error) {\n      console.error(error)\n      const message = getApiErrorMessage(error, localization.error.automationCreate)\n      showToast(message, 'error', { timeout: false })\n    }\n  }\n</script>"], "names": ["usePageTitle", "api", "usePrefectApi", "routes", "useWorkspaceRoutes", "router", "useRouter", "crumbs", "getActions", "getTrigger", "useCreateAutomationQueryParams", "automation", "__temp", "__restore", "_withAsyncContext", "getAutomationTemplate", "trigger", "actions", "submit", "showToast", "localization", "error", "message", "getApiErrorMessage"], "mappings": "icAyBEA,EAAa,mBAAmB,EAEhC,MAAMC,EAAMC,EAAc,EACpBC,EAASC,EAAmB,EAC5BC,EAASC,EAAU,EAEnBC,EAAsB,CAC1B,CAAE,KAAM,cAAe,GAAIJ,EAAO,aAAc,EAChD,CAAE,KAAM,QAAS,CACnB,EAEM,CAAE,WAAAK,EAAY,WAAAC,CAAW,EAAIC,EAA+B,EAE5DC,GAAmB,CAAAC,EAAAC,CAAA,EAAAC,EAAA,IAAAC,EAAsB,CAAA,mBAE/C,eAAeA,GAAsD,CACnE,MAAMJ,EAAkC,CAAC,EAEnC,CAACK,EAASC,CAAO,EAAI,MAAM,QAAQ,IAAI,CAC3CR,EAAW,EACXD,EAAW,CAAA,CACZ,EAED,OAAIQ,IACFL,EAAW,QAAUK,GAGnBC,IACFN,EAAW,QAAUM,GAGhBN,CAAA,CAGT,eAAeO,EAAOP,EAAuC,CACvD,GAAA,CACI,MAAAV,EAAI,YAAY,iBAAiBU,CAAU,EAEvCQ,EAAAC,EAAa,QAAQ,gBAAgB,EAExCf,EAAA,KAAKF,EAAO,aAAa,QACzBkB,EAAO,CACd,QAAQ,MAAMA,CAAK,EACnB,MAAMC,EAAUC,EAAmBF,EAAOD,EAAa,MAAM,gBAAgB,EAC7ED,EAAUG,EAAS,QAAS,CAAE,QAAS,GAAO,CAAA,CAChD"}