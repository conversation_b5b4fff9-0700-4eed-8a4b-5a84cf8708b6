prefect-version: null
name: null
description: "Store code within a GCS bucket"

required_inputs:
  bucket: "The bucket to store and retrieve this code from"

build: null

push: 
  - prefect_gcp.deployments.steps.push_to_gcs:
      id: "push_code"
      requires: "prefect-gcp>=0.4.3"
      bucket: "{{ bucket }}" 
      folder: "{{ name }}"

pull:
  - prefect_gcp.deployments.steps.pull_from_gcs:
      id: "pull_code"
      requires: "prefect-gcp>=0.4.3"
      bucket: "{{ push_code.bucket }}"
      folder: "{{ pull_code.folder }}"

deployments:
  - name: null
    version: null
    tags: []
    description: null
    schedule: {}
    flow_name: null
    entrypoint: null
    parameters: {}
    work_pool:
      name: null
      work_queue_name: null
      job_variables: {}