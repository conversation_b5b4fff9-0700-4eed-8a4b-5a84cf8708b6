import{d as f,h as x,V as C,W as c,j as r,aC as I,aD as b,I as h,k as y,c as E,a as V,p as e,o as k,l as n,m as s,aE as w,z as D,aF as F}from"./index-CUm6gmtO.js";import{u as g}from"./usePageTitle-BEMSydIZ.js";const j=f({__name:"Event",async setup(B){let a,o;const l=x(),u=C(),v=c("eventDate"),_=c("eventId"),m=r(()=>I(v.value)),p=b({startDate:m,eventId:[_.value]}),t=([a,o]=h(()=>l.events.getFirstEvent(p.value)),a=await a,o(),a),d=r(()=>[{text:"Event Feed",to:u.events()},{text:t.eventLabel}]);return g(`Event: ${t.eventLabel}`),(L,N)=>{const i=y("p-layout-default");return e(t)?(k(),E(i,{key:0,class:"event"},{header:n(()=>[s(e(D),{crumbs:d.value},{actions:n(()=>[s(e(F),{event:e(t)},null,8,["event"])]),_:1},8,["crumbs"])]),default:n(()=>[s(e(w),{event:e(t)},null,8,["event"])]),_:1})):V("",!0)}}});export{j as default};
//# sourceMappingURL=Event-C3OOk3n9.js.map
