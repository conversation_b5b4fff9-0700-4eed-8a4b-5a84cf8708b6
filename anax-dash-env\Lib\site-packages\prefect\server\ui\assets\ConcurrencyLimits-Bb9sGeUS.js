import{d as w,cR as d,al as C,k as u,c as M,o as y,l as a,m as o,a6 as r,p as l,z as i,cS as h,cT as x,cU as R,cV as G}from"./index-CUm6gmtO.js";const v=w({__name:"ConcurrencyLimits",setup(T){const{showModal:t,open:m}=d(),{showModal:e,open:b}=d(),_=[{label:"Global"},{label:"Task Run"}],c=C("tab","Global");return(U,s)=>{const p=u("p-button"),k=u("p-tabs"),f=u("p-layout-default");return y(),M(f,{class:"concurrency-limits"},{header:a(()=>[o(l(i),{crumbs:[{text:"Concurrency"}]})]),default:a(()=>[o(k,{selected:l(c),"onUpdate:selected":s[2]||(s[2]=n=>r(c)?c.value=n:null),tabs:_},{global:a(()=>[o(l(i),{size:"lg",crumbs:[{text:"Global Concurrency Limits"}]},{"after-crumbs":a(()=>[o(p,{small:"",icon:"PlusIcon",onClick:l(m)},null,8,["onClick"])]),_:1}),o(l(R),{showModal:l(t),"onUpdate:showModal":s[0]||(s[0]=n=>r(t)?t.value=n:null)},null,8,["showModal"]),o(l(G),{class:"concurrency-limits__global-table"})]),"task-run":a(()=>[o(l(i),{size:"lg",crumbs:[{text:"Task Run Concurrency Limits"}]},{"after-crumbs":a(()=>[o(p,{small:"",icon:"PlusIcon",onClick:l(b)},null,8,["onClick"])]),_:1}),o(l(h),{showModal:l(e),"onUpdate:showModal":s[1]||(s[1]=n=>r(e)?e.value=n:null)},null,8,["showModal"]),o(l(x),{class:"concurrency-limits__task-limits-table"})]),_:1},8,["selected"])]),_:1})}}});export{v as default};
//# sourceMappingURL=ConcurrencyLimits-Bb9sGeUS.js.map
