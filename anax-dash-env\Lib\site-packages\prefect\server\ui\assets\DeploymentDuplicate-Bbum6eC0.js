import{d as D,h as v,W as h,i as w,j as l,k as C,c as b,a as g,o as k,l as s,m as r,p as c,c5 as E,c6 as x,K as p,c7 as m,be as I,Y as B}from"./index-CUm6gmtO.js";import{u as N}from"./usePageTitle-BEMSydIZ.js";const Y=D({__name:"DeploymentDuplicate",setup(S){const a=v(),u=h("deploymentId"),d=w(a.deployments.getDeployment,[u.value],{}),e=l(()=>d.response);function i(t){return"name"in t}async function y(t){try{if(!i(t))throw new Error("Invalid request");const n=await a.deployments.createDeployment(t);p("Deployment created","success"),m.push(I.deployment(n.id))}catch(n){const o=B(n,"Error creating deployment");p(o,"error"),console.warn(n)}}function f(){m.back()}const _=l(()=>e.value?`Duplicate Deployment: ${e.value.name}`:"Duplicate Deployment");return N(_),(t,n)=>{const o=C("p-layout-default");return e.value?(k(),b(o,{key:0,class:"deployment-edit"},{header:s(()=>[r(c(x),{deployment:e.value},null,8,["deployment"])]),default:s(()=>[r(c(E),{deployment:e.value,mode:"duplicate",onCancel:f,onSubmit:y},null,8,["deployment"])]),_:1})):g("",!0)}}});export{Y as default};
//# sourceMappingURL=DeploymentDuplicate-Bbum6eC0.js.map
