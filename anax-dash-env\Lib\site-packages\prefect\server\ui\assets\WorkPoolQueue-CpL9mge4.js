import{d as R,h as D,W as d,j as o,i as f,bu as I,aI as S,al as T,bt as j,k as p,c as F,a as V,o as Y,l as a,m as u,p as e,a6 as z,bz as A,d9 as L,da as v,d1 as O,db as Z}from"./index-CUm6gmtO.js";import{u as E}from"./usePageTitle-BEMSydIZ.js";const K=R({__name:"WorkPoolQueue",setup(G){const c=D(),l=d("workPoolName"),P=o(()=>[l.value]),r=d("workPoolQueueName"),b=o(()=>[r.value]),k={interval:3e5},i=f(c.workPoolQueues.getWorkPoolQueueByName,[l.value,r.value],k),t=o(()=>i.response),_=f(c.workPools.getWorkPoolByName,[l.value],k),q=o(()=>_.response),w=o(()=>{var n;return((n=q.value)==null?void 0:n.type)==="prefect-agent"}),g=o(()=>t.value?`Your work pool ${t.value.name} is ready to go!`:"Your work queue is ready to go!"),y=o(()=>`prefect ${w.value?"agent":"worker"} start --pool "${l.value}" --work-queue "${r.value}"`),Q=o(()=>`Work queues are scoped to a work pool to allow ${w.value?"agents":"workers"} to pull from groups of queues with different priorities.`),{filter:N}=I({workPoolQueues:{name:b},workPools:{name:P}}),W=o(()=>[{label:"Details",hidden:S.xl},{label:"Upcoming Runs"},{label:"Runs"}]),s=T("tab","Details"),{tabs:B}=j(W,s),h=o(()=>r.value?`Work Pool Queue: ${r.value}`:"Work Pool Queue");return E(h),(n,m)=>{const x=p("p-tabs"),C=p("p-layout-well"),$=p("p-layout-default");return t.value?(Y(),F($,{key:0,class:"work-pool-queue"},{header:a(()=>[u(e(Z),{"work-pool-queue":t.value,"work-pool-name":e(l),onUpdate:e(i).refresh},null,8,["work-pool-queue","work-pool-name","onUpdate"])]),default:a(()=>[u(C,{class:"work-pool-queue__body"},{header:a(()=>[u(e(O),{command:y.value,title:g.value,subtitle:Q.value},null,8,["command","title","subtitle"])]),well:a(()=>[u(e(v),{alternate:"","work-pool-name":e(l),"work-pool-queue":t.value},null,8,["work-pool-name","work-pool-queue"])]),default:a(()=>[u(x,{selected:e(s),"onUpdate:selected":m[0]||(m[0]=U=>z(s)?s.value=U:null),tabs:e(B)},{details:a(()=>[u(e(v),{"work-pool-name":e(l),"work-pool-queue":t.value},null,8,["work-pool-name","work-pool-queue"])]),"upcoming-runs":a(()=>[u(e(L),{"work-pool-name":e(l),"work-pool-queue":t.value},null,8,["work-pool-name","work-pool-queue"])]),runs:a(()=>[u(e(A),{filter:e(N),prefix:"runs"},null,8,["filter"])]),_:1},8,["selected","tabs"])]),_:1})]),_:1})):V("",!0)}}});export{K as default};
//# sourceMappingURL=WorkPoolQueue-CpL9mge4.js.map
