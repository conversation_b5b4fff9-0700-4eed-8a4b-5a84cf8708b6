import{d as V,V as B,u as D,W as F,i as N,j as n,k as s,c as _,a as T,o as l,l as e,m as a,n as d,p as o,q,t as I,v as J,x as P,F as Q,y as U,J as W,z as j,A as w,Q as z}from"./index-CUm6gmtO.js";import{u as E}from"./usePageTitle-BEMSydIZ.js";import{u as L}from"./usePrefectApi-DO-y9p9U.js";import"./api-9onuHMbZ.js";import"./mapper-BXnlCs1t.js";const R={class:"automation-card__label"},O=V({__name:"Automation",setup(S){const c=B(),v=D(),f=L(),g=F("automationId"),i=N(f.automations.getAutomation,[g]),t=n(()=>i.response),y=n(()=>{var u;return((u=t.value)==null?void 0:u.name)??""}),A=n(()=>[{text:"Automations",to:c.automations()},{text:y.value}]),b=n(()=>t.value?`Automation: ${t.value.name}`:"Automation");E(b);function k(){v.push(c.automations())}return(u,m)=>{const x=s("p-key-value"),r=s("p-content"),h=s("p-card"),C=s("p-layout-default");return t.value?(l(),_(C,{key:0,class:"automation"},{header:e(()=>[a(o(j),{crumbs:A.value},{actions:e(()=>[a(o(w),{automation:t.value,onUpdate:o(i).refresh},null,8,["automation","onUpdate"]),a(o(z),{automation:t.value,onDelete:k},null,8,["automation"])]),_:1},8,["crumbs"])]),default:e(()=>[a(r,null,{default:e(()=>[a(x,{label:"Description",value:t.value.description},null,8,["value"]),a(r,{secondary:""},{default:e(()=>[m[0]||(m[0]=d("span",{class:"automation-card__label"},"Trigger",-1)),a(o(q),{trigger:t.value.trigger},null,8,["trigger"])]),_:1}),a(r,{secondary:""},{default:e(()=>[d("span",R,J(o(P)("Action",t.value.actions.length)),1),(l(!0),I(Q,null,U(t.value.actions,p=>(l(),_(h,{key:p.id},{default:e(()=>[a(o(W),{action:p},null,8,["action"])]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})):T("",!0)}}});export{O as default};
//# sourceMappingURL=Automation-Dp7jXYDX.js.map
