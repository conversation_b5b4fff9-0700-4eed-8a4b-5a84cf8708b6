{"version": 3, "file": "WorkQueueToWorkPoolQueueRedirect-Ckm8pLs4-yyZZXU2D.js", "sources": ["../../node_modules/@prefecthq/prefect-ui-library/dist/WorkQueueToWorkPoolQueueRedirect-Ckm8pLs4.mjs"], "sourcesContent": ["import { defineComponent as p } from \"vue\";\nimport { useRouteParam as m, useSubscription as i } from \"@prefecthq/vue-compositions\";\nimport { useRouter as n } from \"vue-router\";\nimport { b as a, c } from \"./index-Dycv0mhr.mjs\";\nimport \"@prefecthq/prefect-design\";\nimport \"vee-validate\";\nimport \"@prefecthq/vue-charts\";\nconst W = /* @__PURE__ */ p({\n  __name: \"WorkQueueToWorkPoolQueueRedirect\",\n  setup(k) {\n    const t = m(\"workQueueId\"), s = a(), u = i(s.workQueues.getWorkQueue, [t]), e = n(), r = c();\n    return u.promise().then(({ response: o }) => {\n      if (!o.workPoolName) {\n        e.replace(r.workPools());\n        return;\n      }\n      e.replace(r.workPoolQueue(o.workPoolName, o.name));\n    }), () => {\n    };\n  }\n});\nexport {\n  W as default\n};\n//# sourceMappingURL=WorkQueueToWorkPoolQueueRedirect-Ckm8pLs4.mjs.map\n"], "names": ["W", "p", "k", "t", "m", "a", "u", "i", "e", "n", "c", "o"], "mappings": "2EAOK,MAACA,EAAoBC,EAAE,CAC1B,OAAQ,mCACR,MAAMC,EAAG,CACP,MAAMC,EAAIC,EAAE,aAAa,EAAG,EAAIC,EAAG,EAAEC,EAAIC,EAAE,EAAE,WAAW,aAAc,CAACJ,CAAC,CAAC,EAAGK,EAAIC,EAAG,EAAE,EAAIC,EAAG,EAC5F,OAAOJ,EAAE,UAAU,KAAK,CAAC,CAAE,SAAUK,KAAQ,CAC3C,GAAI,CAACA,EAAE,aAAc,CACnBH,EAAE,QAAQ,EAAE,WAAW,EACvB,MACR,CACMA,EAAE,QAAQ,EAAE,cAAcG,EAAE,aAAcA,EAAE,IAAI,CAAC,CAClD,CAAA,EAAG,IAAM,CACT,CACL,CACA,CAAC", "x_google_ignoreList": [0]}