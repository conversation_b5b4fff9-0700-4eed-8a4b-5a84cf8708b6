import{d as k,h as y,W as i,j as o,bF as m,k as b,c as _,a as T,o as d,l as a,m as t,p as c,cl as f,cm as v}from"./index-CUm6gmtO.js";import{u as g}from"./usePageTitle-BEMSydIZ.js";const x=k({__name:"BlocksCatalogView",setup(B){const s=y(),l=i("blockTypeSlug"),n=o(()=>l.value?[l.value]:null),u=m(s.blockTypes.getBlockTypeBySlug,n),e=o(()=>u.response),p=o(()=>e.value?`Block Type: ${e.value.name}`:null);return g(p),(C,S)=>{const r=b("p-layout-default");return e.value?(d(),_(r,{key:0,class:"blocks-catalog-view"},{header:a(()=>[t(c(v),{"block-type":e.value},null,8,["block-type"])]),default:a(()=>[t(c(f),{"block-type":e.value},null,8,["block-type"])]),_:1})):T("",!0)}}});export{x as default};
//# sourceMappingURL=BlocksCatalogView-Dw1cxMNF.js.map
