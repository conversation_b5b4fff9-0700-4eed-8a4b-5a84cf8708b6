import{d as m,h as i,i as k,j as s,k as _,c as a,o,l as r,t as f,a as P,F as h,p as t,d2 as w,d3 as y,m as v,d4 as x}from"./index-CUm6gmtO.js";import{u as g}from"./usePageTitle-BEMSydIZ.js";const I=m({__name:"WorkPools",setup(C){const n=i(),l={interval:3e4},e=k(n.workPools.getWorkPools,[{}],l),c=s(()=>e.response??[]),p=s(()=>e.executed&&c.value.length==0),u=s(()=>e.executed);return g("Work Pools"),(b,B)=>{const d=_("p-layout-default");return o(),a(d,{class:"work-pools"},{header:r(()=>[v(t(x))]),default:r(()=>[u.value?(o(),f(h,{key:0},[p.value?(o(),a(t(w),{key:0})):(o(),a(t(y),{key:1,onUpdate:t(e).refresh},null,8,["onUpdate"]))],64)):P("",!0)]),_:1})}}});export{I as default};
//# sourceMappingURL=WorkPools-DR-pn8lP.js.map
