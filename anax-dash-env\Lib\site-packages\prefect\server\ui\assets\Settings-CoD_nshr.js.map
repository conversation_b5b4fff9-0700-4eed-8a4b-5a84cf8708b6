{"version": 3, "file": "Settings-CoD_nshr.js", "sources": ["../../src/components/SettingsCodeBlock.vue", "../../src/pages/Settings.vue"], "sourcesContent": ["<template>\n  <div class=\"settings-block\">\n    <p-code multiline>\n      <div v-for=\"(section, index) in settingSections\" :key=\"index\" class=\"settings-block--code-line\">\n        {{ section[0] }}: {{ section[1] }}\n      </div>\n    </p-code>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\n  import { PCode } from '@prefecthq/prefect-design'\n  import { computed } from 'vue'\n  import { ServerSettings } from '@/models/ServerSettings'\n\n  const props = defineProps<{\n    engineSettings: ServerSettings,\n  }>()\n  const settingSections = computed(() => Object.entries(props.engineSettings))\n</script>\n\n<style>\n.settings-block--code-line {\n  @apply whitespace-pre-wrap;\n}\n</style>\n", "<template>\n  <p-layout-default class=\"settings\">\n    <template #header>\n      <PageHeading :crumbs=\"crumbs\">\n        <template #actions>\n          <p-key-value class=\"settings__version\" label=\"Version\" :value=\"version\" alternate />\n        </template>\n      </PageHeading>\n    </template>\n\n    <p-label label=\"Theme\">\n      <p-theme-toggle />\n    </p-label>\n\n    <p-label label=\"Color Mode\" class=\"settings__color-mode\">\n      <ColorModeSelect v-model:selected=\"activeColorMode\" />\n    </p-label>\n\n    <p-label label=\"Server Settings\">\n      <SettingsCodeBlock class=\"settings__code-block\" :engine-settings=\"engineSettings\" />\n    </p-label>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { PageHeading, ColorModeSelect } from '@prefecthq/prefect-ui-library'\n  import SettingsCodeBlock from '@/components/SettingsCodeBlock.vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { usePrefectApi } from '@/compositions/usePrefectApi'\n  import { activeColorMode } from '@/utilities/colorMode'\n\n  const crumbs = [{ text: 'Settings' }]\n\n  const api = usePrefectApi()\n  const [engineSettings, version] = await Promise.all([\n    api.admin.getSettings(),\n    api.admin.getVersion(),\n  ])\n\n  usePageTitle('Settings')\n</script>\n\n<style>\n.settings__version { @apply\n  w-auto\n}\n\n.settings__color-mode { @apply\n  w-96\n  max-w-full\n}\n\n.settings__code-block { @apply\n  max-w-full\n  overflow-x-auto\n}\n</style>"], "names": ["props", "__props", "settingSections", "computed", "crumbs", "api", "usePrefectApi", "engineSettings", "version", "__temp", "__restore", "_withAsyncContext", "usePageTitle"], "mappings": "6ZAeE,MAAMA,EAAQC,EAGRC,EAAkBC,EAAS,IAAM,OAAO,QAAQH,EAAM,cAAc,CAAC,yPCa3E,MAAMI,EAAS,CAAC,CAAE,KAAM,WAAY,EAE9BC,EAAMC,EAAc,EACpB,CAACC,EAAgBC,CAAO,GAAU,CAAAC,EAAAC,CAAA,EAAAC,EAAA,IAAA,QAAQ,IAAI,CAClDN,EAAI,MAAM,YAAY,EACtBA,EAAI,MAAM,WAAW,CAAA,CACtB,CAAA,mBAED,OAAAO,EAAa,UAAU"}