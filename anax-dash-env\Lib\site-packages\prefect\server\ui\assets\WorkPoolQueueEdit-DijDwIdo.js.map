{"version": 3, "file": "WorkPoolQueueEdit-DijDwIdo.js", "sources": ["../../src/pages/WorkPoolQueueEdit.vue"], "sourcesContent": ["<template>\n  <p-layout-default>\n    <template #header>\n      <PageHeadingWorkPoolQueueEdit :work-pool-name=\"workPoolName\" :work-pool-queue-name=\"workPoolQueueName\" />\n    </template>\n\n    <WorkPoolQueueEditForm :work-pool-name=\"workPoolName\" :work-pool-queue=\"workPoolQueue\" />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { useWorkspaceApi, PageHeadingWorkPoolQueueEdit, WorkPoolQueueEditForm } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam } from '@prefecthq/vue-compositions'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const api = useWorkspaceApi()\n  const workPoolName = useRouteParam('workPoolName')\n  const workPoolQueueName = useRouteParam('workPoolQueueName')\n\n  const workPoolQueue = await api.workPoolQueues.getWorkPoolQueueByName(workPoolName.value, workPoolQueueName.value)\n\n  usePageTitle('Edit Work Pool Queue')\n</script>"], "names": ["api", "useWorkspaceApi", "workPoolName", "useRouteParam", "workPoolQueueName", "workPoolQueue", "__temp", "__restore", "_withAsyncContext", "usePageTitle"], "mappings": "mOAeE,MAAMA,EAAMC,EAAgB,EACtBC,EAAeC,EAAc,cAAc,EAC3CC,EAAoBD,EAAc,mBAAmB,EAErDE,GAAgB,CAAAC,EAAAC,CAAA,EAAAC,EAAA,IAAMR,EAAI,eAAe,uBAAuBE,EAAa,MAAOE,EAAkB,KAAK,CAAA,mBAEjH,OAAAK,EAAa,sBAAsB"}