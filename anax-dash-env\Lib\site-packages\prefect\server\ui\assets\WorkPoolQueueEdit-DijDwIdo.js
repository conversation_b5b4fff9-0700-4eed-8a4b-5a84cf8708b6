import{d as k,h as c,W as t,I as w,k as _,c as d,o as i,l as r,m as n,p as o,de as f,df as P}from"./index-CUm6gmtO.js";import{u as Q}from"./usePageTitle-BEMSydIZ.js";const x=k({__name:"WorkPoolQueueEdit",async setup(N){let e,u;const s=c(),a=t("workPoolName"),l=t("workPoolQueueName"),p=([e,u]=w(()=>s.workPoolQueues.getWorkPoolQueueByName(a.value,l.value)),e=await e,u(),e);return Q("Edit Work Pool Queue"),(h,y)=>{const m=_("p-layout-default");return i(),d(m,null,{header:r(()=>[n(o(P),{"work-pool-name":o(a),"work-pool-queue-name":o(l)},null,8,["work-pool-name","work-pool-queue-name"])]),default:r(()=>[n(o(f),{"work-pool-name":o(a),"work-pool-queue":o(p)},null,8,["work-pool-name","work-pool-queue"])]),_:1})}}});export{x as default};
//# sourceMappingURL=WorkPoolQueueEdit-DijDwIdo.js.map
