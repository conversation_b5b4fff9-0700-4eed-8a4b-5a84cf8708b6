import{d as b,h as d,u as i,W as _,j as o,bF as f,k as v,c as D,a as B,o as h,l as c,m as s,p as u,cs as C,ct as w,be as g}from"./index-CUm6gmtO.js";import{u as x}from"./usePageTitle-BEMSydIZ.js";const T=b({__name:"BlockView",setup(y){const n=d(),l=i(),t=_("blockDocumentId"),a=o(()=>t.value?[t.value]:null),r=f(n.blockDocuments.getBlockDocument,a),e=o(()=>r.response),m=()=>{l.push(g.blocks())},k=o(()=>e.value?`Block: ${e.value.name}`:"Block");return x(k),(V,I)=>{const p=v("p-layout-default");return e.value?(h(),D(p,{key:0,class:"block-view"},{header:c(()=>[s(u(w),{"block-document":e.value,onDelete:m},null,8,["block-document"])]),default:c(()=>[s(u(C),{"block-document":e.value},null,8,["block-document"])]),_:1})):B("",!0)}}});export{T as default};
//# sourceMappingURL=BlockView-szA8m5oz.js.map
