{"version": 3, "file": "WorkPoolCreate-DPnNeTdz.js", "sources": ["../../src/pages/WorkPoolCreate.vue"], "sourcesContent": ["<template>\n  <p-layout-default>\n    <template #header>\n      <PageHeadingWorkPoolCreate />\n    </template>\n    <WorkPoolCreateWizard />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { PageHeadingWorkPoolCreate, WorkPoolCreateWizard } from '@prefecthq/prefect-ui-library'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  usePageTitle('Create Work Pool')\n</script>"], "names": ["usePageTitle"], "mappings": "6LAaE,OAAAA,EAAa,kBAAkB"}