{"version": 3, "file": "WorkPool-B0R8Qlsu.js", "sources": ["../../src/pages/WorkPool.vue"], "sourcesContent": ["<template>\n  <p-layout-well v-if=\"workPool\" class=\"work-pool\">\n    <template #header>\n      <PageHeadingWorkPool :work-pool=\"workPool\" @update=\"workPoolSubscription.refresh\" />\n      <template v-if=\"showCodeBanner\">\n        <CodeBanner class=\"work-pool__code-banner\" :command=\"codeBannerCliCommand\" title=\"Your work pool is almost ready!\" subtitle=\"Run this command to start.\" />\n      </template>\n    </template>\n    <p-tabs v-model:selected=\"tab\" :tabs=\"tabs\">\n      <template #details>\n        <WorkPoolDetails :work-pool=\"workPool\" />\n      </template>\n\n      <template #runs>\n        <FlowRunFilteredList :filter=\"flowRunFilter\" prefix=\"runs\" />\n      </template>\n\n      <template #work-queues>\n        <WorkPoolQueuesTable :work-pool-name=\"workPoolName\" />\n      </template>\n\n      <template #workers>\n        <WorkersTable :work-pool-name=\"workPoolName\" />\n      </template>\n\n      <template #deployments>\n        <DeploymentList :filter=\"deploymentsFilter\" />\n      </template>\n    </p-tabs>\n\n    <template #well>\n      <WorkPoolDetails alternate :work-pool=\"workPool\" />\n    </template>\n  </p-layout-well>\n</template>\n\n<script lang=\"ts\" setup>\n  import { media } from '@prefecthq/prefect-design'\n  import {\n    useWorkspaceApi,\n    PageHeadingWorkPool,\n    WorkPoolDetails,\n    FlowRunFilteredList,\n    WorkPoolQueuesTable,\n    useFlowRunsFilter,\n    useTabs,\n    WorkersTable,\n    CodeBanner,\n    DeploymentList,\n    useDeploymentsFilter\n  } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam, useRouteQueryParam, useSubscription } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const api = useWorkspaceApi()\n  const workPoolName = useRouteParam('workPoolName')\n\n  const subscriptionOptions = {\n    interval: 300000,\n  }\n  const workPoolSubscription = useSubscription(api.workPools.getWorkPoolByName, [workPoolName.value], subscriptionOptions)\n  const workPool = computed(() => workPoolSubscription.response)\n  const isAgentWorkPool = computed(() => workPool.value?.type === 'prefect-agent')\n\n  const computedTabs = computed(() => [\n    { label: 'Details', hidden: media.xl },\n    { label: 'Runs' },\n    { label: 'Work Queues' },\n    { label: 'Workers', hidden: isAgentWorkPool.value },\n    { label: 'Deployments' },\n  ])\n\n  const tab = useRouteQueryParam('tab', 'Details')\n  const { tabs } = useTabs(computedTabs, tab)\n\n  const showCodeBanner = computed(() => workPool.value?.status !== 'ready')\n  const codeBannerCliCommand = computed(() => `prefect ${isAgentWorkPool.value ? 'agent' : 'worker'} start --pool \"${workPool.value?.name}\"`)\n\n  const { filter: flowRunFilter } = useFlowRunsFilter({\n    workPools: {\n      name: [workPoolName.value],\n    },\n  })\n\n  const { filter: deploymentsFilter } = useDeploymentsFilter({\n    workPools: {\n      name: [workPoolName.value],\n    },\n  })\n\n  const title = computed(() => {\n    if (!workPool.value) {\n      return 'Work Pool'\n    }\n    return `Work Pool: ${workPool.value.name}`\n  })\n\n  usePageTitle(title)\n</script>\n\n<style>\n.work-pool__code-banner { @apply\n  mt-4\n}\n</style>"], "names": ["api", "useWorkspaceApi", "workPoolName", "useRouteParam", "subscriptionOptions", "workPoolSubscription", "useSubscription", "workPool", "computed", "isAgentWorkPool", "_a", "computedTabs", "media", "tab", "useRouteQueryParam", "tabs", "useTabs", "showCodeBanner", "codeBannerCliCommand", "flowRunFilter", "useFlowRunsFilter", "deploymentsFilter", "useDeploymentsFilter", "title", "usePageTitle"], "mappings": "kTAuDE,MAAMA,EAAMC,EAAgB,EACtBC,EAAeC,EAAc,cAAc,EAE3CC,EAAsB,CAC1B,SAAU,GACZ,EACMC,EAAuBC,EAAgBN,EAAI,UAAU,kBAAmB,CAACE,EAAa,KAAK,EAAGE,CAAmB,EACjHG,EAAWC,EAAS,IAAMH,EAAqB,QAAQ,EACvDI,EAAkBD,EAAS,IAAA,OAAM,QAAAE,EAAAH,EAAS,QAAT,YAAAG,EAAgB,QAAS,gBAAe,EAEzEC,EAAeH,EAAS,IAAM,CAClC,CAAE,MAAO,UAAW,OAAQI,EAAM,EAAG,EACrC,CAAE,MAAO,MAAO,EAChB,CAAE,MAAO,aAAc,EACvB,CAAE,MAAO,UAAW,OAAQH,EAAgB,KAAM,EAClD,CAAE,MAAO,aAAc,CAAA,CACxB,EAEKI,EAAMC,EAAmB,MAAO,SAAS,EACzC,CAAE,KAAAC,CAAS,EAAAC,EAAQL,EAAcE,CAAG,EAEpCI,EAAiBT,EAAS,IAAA,OAAM,QAAAE,EAAAH,EAAS,QAAT,YAAAG,EAAgB,UAAW,QAAO,EAClEQ,EAAuBV,EAAS,IAAM,OAAA,iBAAWC,EAAgB,MAAQ,QAAU,QAAQ,mBAAkBC,EAAAH,EAAS,QAAT,YAAAG,EAAgB,IAAI,IAAG,EAEpI,CAAE,OAAQS,CAAc,EAAIC,EAAkB,CAClD,UAAW,CACT,KAAM,CAAClB,EAAa,KAAK,CAAA,CAC3B,CACD,EAEK,CAAE,OAAQmB,CAAkB,EAAIC,EAAqB,CACzD,UAAW,CACT,KAAM,CAACpB,EAAa,KAAK,CAAA,CAC3B,CACD,EAEKqB,EAAQf,EAAS,IAChBD,EAAS,MAGP,cAAcA,EAAS,MAAM,IAAI,GAF/B,WAGV,EAED,OAAAiB,EAAaD,CAAK"}