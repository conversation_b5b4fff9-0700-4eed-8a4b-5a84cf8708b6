import{d as y,h as m,W as k,i as _,j as s,G as o,k as v,c as e,o as t,l as n,a as r,t as d,p as c,aH as C,F as g,aN as h,aO as x}from"./index-CUm6gmtO.js";import{u as A}from"./usePageTitle-BEMSydIZ.js";const T=y({__name:"ArtifactKey",setup(B){const l=m(),u=k("artifactKey"),i=_(l.artifacts.getArtifactCollection,[u]),a=s(()=>i.response),f=s(()=>a.value?`${o.info.artifact}: ${a.value.key}`:o.info.artifact);return A(f),(F,K)=>{const p=v("p-layout-default");return t(),e(p,{class:"artifact"},{header:n(()=>[a.value?(t(),e(c(x),{key:0,artifact:a.value},null,8,["artifact"])):r("",!0)]),default:n(()=>[a.value?(t(),e(c(C),{key:0,artifact:a.value},null,8,["artifact"])):r("",!0),a.value?(t(),d(g,{key:1},[a.value.key?(t(),e(c(h),{key:0,"artifact-key":a.value.key},null,8,["artifact-key"])):r("",!0)],64)):r("",!0)]),_:1})}}});export{T as default};
//# sourceMappingURL=ArtifactKey-C0qc9eWD.js.map
