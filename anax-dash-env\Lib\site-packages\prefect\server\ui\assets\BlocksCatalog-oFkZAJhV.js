import{d as b,h as m,ae as y,j as o,ci as f,i as _,k as d,c as C,o as v,l as s,m as t,p as l,cj as T,ck as g}from"./index-CUm6gmtO.js";import{u as B}from"./usePageTitle-BEMSydIZ.js";const S=b({__name:"BlocksCatalog",setup(h){const c=m(),a=y(null),p=o(()=>a.value?[a.value]:[]),{filter:n}=f({blockSchemas:{blockCapabilities:p}}),i=_(c.blockTypes.getBlockTypes,[n]),u=o(()=>i.response??[]);return B("Blocks Catalog"),(x,e)=>{const r=d("p-layout-default");return v(),C(r,{class:"blocks-catalog"},{header:s(()=>[t(l(g))]),default:s(()=>[t(l(T),{capability:a.value,"onUpdate:capability":e[0]||(e[0]=k=>a.value=k),"block-types":u.value},null,8,["capability","block-types"])]),_:1})}}});export{S as default};
//# sourceMappingURL=BlocksCatalog-oFkZAJhV.js.map
