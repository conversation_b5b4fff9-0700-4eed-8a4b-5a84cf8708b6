{"version": 3, "file": "BlocksCatalogCreate-6VSm5aRl.js", "sources": ["../../src/pages/BlocksCatalogCreate.vue"], "sourcesContent": ["<template>\n  <p-layout-default v-if=\"blockType\" class=\"blocks-catalog-create\">\n    <template #header>\n      <PageHeadingBlocksCatalogCreate :block-type=\"blockType\" />\n    </template>\n\n    <template v-if=\"blockType\">\n      <BlockTypeCardLayout :block-type=\"blockType\">\n        <template v-if=\"blockSchema\">\n          <BlockSchemaCreateForm :key=\"blockSchema.id\" :block-schema=\"blockSchema\" v-on=\"{ submit, cancel }\" />\n        </template>\n      </BlockTypeCardLayout>\n    </template>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { showToast } from '@prefecthq/prefect-design'\n  import { PageHeadingBlocksCatalogCreate, BlockTypeCardLayout, BlockSchemaCreateForm, BlockDocumentCreateNamed, asSingle, useWorkspaceApi } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam, useRouteQueryParam, useSubscriptionWithDependencies } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { useRouter } from 'vue-router'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { routes } from '@/router'\n\n  const api = useWorkspaceApi()\n  const router = useRouter()\n  const redirect = useRouteQueryParam('redirect')\n\n  const blockTypeSlugParam = useRouteParam('blockTypeSlug')\n  const blockTypeSubscriptionArgs = computed<Parameters<typeof api.blockTypes.getBlockTypeBySlug> | null>(() => {\n    if (!blockTypeSlugParam.value) {\n      return null\n    }\n\n    return [blockTypeSlugParam.value]\n  })\n\n  const blockTypeSubscription = useSubscriptionWithDependencies(api.blockTypes.getBlockTypeBySlug, blockTypeSubscriptionArgs)\n  const blockType = computed(() => blockTypeSubscription.response)\n\n  const blockSchemaSubscriptionArgs = computed<Parameters<typeof api.blockSchemas.getBlockSchemaForBlockType> | null>(() => {\n    if (!blockType.value) {\n      return null\n    }\n\n    return [blockType.value.id]\n  })\n\n  const blockSchemaSubscription = useSubscriptionWithDependencies(api.blockSchemas.getBlockSchemaForBlockType, blockSchemaSubscriptionArgs)\n  const blockSchema = computed(() => blockSchemaSubscription.response)\n\n  function submit(request: BlockDocumentCreateNamed): void {\n    api.blockDocuments\n      .createBlockDocument(request)\n      .then(({ id }) => onSuccess(id))\n      .catch(err => {\n        showToast('Failed to create block', 'error')\n        console.error(err)\n      })\n  }\n\n  function cancel(): void {\n    router.back()\n  }\n\n  function onSuccess(id: string): void {\n    showToast('Block created successfully', 'success')\n\n    if (redirect.value) {\n      const route = router.resolve(asSingle(redirect.value))\n\n      router.push(route)\n      return\n    }\n\n    router.push(routes.block(id))\n  }\n\n  const title = computed<string>(() => {\n    if (blockType.value) {\n      return `Create ${blockType.value.name} Block`\n    }\n    return 'Create Block'\n  })\n\n  usePageTitle(title)\n</script>"], "names": ["api", "useWorkspaceApi", "router", "useRouter", "redirect", "useRouteQueryParam", "blockTypeSlugParam", "useRouteParam", "blockTypeSubscriptionArgs", "computed", "blockTypeSubscription", "useSubscriptionWithDependencies", "blockType", "blockSchemaSubscriptionArgs", "blockSchemaSubscription", "blockSchema", "submit", "request", "id", "onSuccess", "err", "showToast", "cancel", "route", "as<PERSON>ingle", "routes", "title", "usePageTitle"], "mappings": "mSAyBE,MAAMA,EAAMC,EAAgB,EACtBC,EAASC,EAAU,EACnBC,EAAWC,EAAmB,UAAU,EAExCC,EAAqBC,EAAc,eAAe,EAClDC,EAA4BC,EAAsE,IACjGH,EAAmB,MAIjB,CAACA,EAAmB,KAAK,EAHvB,IAIV,EAEKI,EAAwBC,EAAgCX,EAAI,WAAW,mBAAoBQ,CAAyB,EACpHI,EAAYH,EAAS,IAAMC,EAAsB,QAAQ,EAEzDG,EAA8BJ,EAAgF,IAC7GG,EAAU,MAIR,CAACA,EAAU,MAAM,EAAE,EAHjB,IAIV,EAEKE,EAA0BH,EAAgCX,EAAI,aAAa,2BAA4Ba,CAA2B,EAClIE,EAAcN,EAAS,IAAMK,EAAwB,QAAQ,EAEnE,SAASE,EAAOC,EAAyC,CACvDjB,EAAI,eACD,oBAAoBiB,CAAO,EAC3B,KAAK,CAAC,CAAE,GAAAC,CAAG,IAAMC,EAAUD,CAAE,CAAC,EAC9B,MAAaE,GAAA,CACZC,EAAU,yBAA0B,OAAO,EAC3C,QAAQ,MAAMD,CAAG,CAAA,CAClB,CAAA,CAGL,SAASE,GAAe,CACtBpB,EAAO,KAAK,CAAA,CAGd,SAASiB,EAAUD,EAAkB,CAGnC,GAFAG,EAAU,6BAA8B,SAAS,EAE7CjB,EAAS,MAAO,CAClB,MAAMmB,EAAQrB,EAAO,QAAQsB,EAASpB,EAAS,KAAK,CAAC,EAErDF,EAAO,KAAKqB,CAAK,EACjB,MAAA,CAGFrB,EAAO,KAAKuB,EAAO,MAAMP,CAAE,CAAC,CAAA,CAGxB,MAAAQ,EAAQjB,EAAiB,IACzBG,EAAU,MACL,UAAUA,EAAU,MAAM,IAAI,SAEhC,cACR,EAED,OAAAe,EAAaD,CAAK"}