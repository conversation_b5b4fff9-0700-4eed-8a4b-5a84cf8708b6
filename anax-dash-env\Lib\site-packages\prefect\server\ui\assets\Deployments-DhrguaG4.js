import{d,h as i,i as y,j as o,k as _,c as a,o as t,l as n,t as f,a as k,F as g,p as s,bV as h,bS as x,m as C,bW as b}from"./index-CUm6gmtO.js";import{u as v}from"./usePageTitle-BEMSydIZ.js";const N=d({__name:"Deployments",setup(D){const l=i(),c={interval:3e4},e=y(l.deployments.getDeployments,[{}],c),p=o(()=>e.response??[]),r=o(()=>e.executed&&p.value.length===0),m=o(()=>e.executed);return v("Deployments"),(B,V)=>{const u=_("p-layout-default");return t(),a(u,{class:"deployments"},{header:n(()=>[C(s(b))]),default:n(()=>[m.value?(t(),f(g,{key:0},[r.value?(t(),a(s(h),{key:0})):(t(),a(s(x),{key:1,onDelete:s(e).refresh},null,8,["onDelete"]))],64)):k("",!0)]),_:1})}}});export{N as default};
//# sourceMappingURL=Deployments-DhrguaG4.js.map
