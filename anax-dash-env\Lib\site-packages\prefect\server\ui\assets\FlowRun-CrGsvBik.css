.flow-run-graphs{position:relative;--flow-run-graphs-panel-width: 320px}.flow-run-graphs__graph-panel-container{position:relative;display:grid;grid-template-columns:repeat(1,minmax(0,1fr));gap:.5rem;overflow:hidden}.flow-run-graphs--fullscreen{position:static;z-index:20}.flow-run-graphs__graphs{transition-property:width;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s;width:100%}.flow-run-graphs--show-panel .flow-run-graphs__graphs{width:calc(100% - var(--flow-run-graphs-panel-width) - .5rem)}.flow-run-graphs__flow-run{overflow:hidden;border-radius:.25rem}.flow-run-graphs__panel{position:absolute;right:0;top:0;bottom:0;--tw-translate-x: 100%;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));border-radius:.25rem;transition-property:transform;transition-timing-function:cubic-bezier(.4,0,.2,1);transition-duration:.15s;width:var(--flow-run-graphs-panel-width)}.flow-run-graphs--fullscreen .flow-run-graphs__panel{top:1rem;right:1rem;bottom:auto;background-color:var(--p-color-bg-floating)}.flow-run-graphs--show-panel .flow-run-graphs__panel{--tw-translate-x: 0px;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.flow-run{align-items:flex-start}.flow-run__logs{max-height:100vh}.flow-run__header-meta{display:flex;align-items:center;gap:.5rem}@media (min-width: 1280px){.flow-run__header-meta{display:none}}.flow-run__job-variables,.flow-run__parameters{padding:.75rem 1rem}
