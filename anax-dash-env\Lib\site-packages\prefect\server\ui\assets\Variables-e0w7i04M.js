import{d as m,ae as d,h as _,j as o,i as v,G as b,k as h,c as r,o as s,l as c,t as k,a as y,F as C,p as l,cW as x,cX as g,m as D,cY as V}from"./index-CUm6gmtO.js";import{u as B}from"./usePageTitle-BEMSydIZ.js";const j=m({__name:"Variables",setup(w){const n=d(),a=()=>{var e;t.value.refresh(),(e=n.value)==null||e.refreshSubscriptions()},u=_(),t=o(()=>v(u.variables.getVariables)),p=o(()=>{var e;return t.value.executed&&((e=t.value.response)==null?void 0:e.length)===0}),i=o(()=>t.value.executed);return B(b.info.variables),(e,F)=>{const f=h("p-layout-default");return s(),r(f,{class:"variables"},{header:c(()=>[D(l(V),{onCreate:a})]),default:c(()=>[i.value?(s(),k(C,{key:0},[p.value?(s(),r(l(x),{key:0,onCreate:a})):(s(),r(l(g),{key:1,ref_key:"table",ref:n,onDelete:a,onUpdate:a},null,512))],64)):y("",!0)]),_:1})}}});export{j as default};
//# sourceMappingURL=Variables-e0w7i04M.js.map
