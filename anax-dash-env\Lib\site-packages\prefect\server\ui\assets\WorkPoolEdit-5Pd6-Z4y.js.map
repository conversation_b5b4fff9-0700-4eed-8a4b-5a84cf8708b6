{"version": 3, "file": "WorkPoolEdit-5Pd6-Z4y.js", "sources": ["../../src/pages/WorkPoolEdit.vue"], "sourcesContent": ["<template>\n  <p-layout-default>\n    <template #header>\n      <PageHeadingWorkPoolEdit :work-pool=\"workPool\" />\n    </template>\n\n    <WorkPoolEditForm :work-pool=\"workPool\" />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { useWorkspaceApi, PageHeadingWorkPoolEdit, WorkPoolEditForm } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam } from '@prefecthq/vue-compositions'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const api = useWorkspaceApi()\n  const workPoolName = useRouteParam('workPoolName')\n\n  const workPool = await api.workPools.getWorkPoolByName(workPoolName.value)\n\n\n  usePageTitle('Create Work Pool')\n</script>"], "names": ["api", "useWorkspaceApi", "workPoolName", "useRouteParam", "workPool", "__temp", "__restore", "_withAsyncContext", "usePageTitle"], "mappings": "yNAeE,MAAMA,EAAMC,EAAgB,EACtBC,EAAeC,EAAc,cAAc,EAE3CC,GAAiB,CAAAC,EAAAC,CAAA,EAAAC,EAAA,IAAAP,EAAI,UAAU,kBAAkBE,EAAa,KAAK,CAAA,mBAGzE,OAAAM,EAAa,kBAAkB"}