import{d as v,j as h,t as u,o as i,m as e,l as t,F as C,y as x,v as f,p as n,dy as B,I as V,k as l,c as w,dz as A,a6 as F,dA as p,z as P}from"./index-CUm6gmtO.js";import{u as j}from"./usePageTitle-BEMSydIZ.js";import{u as z}from"./usePrefectApi-DO-y9p9U.js";import"./api-9onuHMbZ.js";import"./mapper-BXnlCs1t.js";const M={class:"settings-block"},T=v({__name:"SettingsCodeBlock",props:{engineSettings:{}},setup(d){const s=d,o=h(()=>Object.entries(s.engineSettings));return(g,c)=>(i(),u("div",M,[e(n(B),{multiline:""},{default:t(()=>[(i(!0),u(C,null,x(o.value,(a,r)=>(i(),u("div",{key:r,class:"settings-block--code-line"},f(a[0])+": "+f(a[1]),1))),128))]),_:1})]))}}),O=v({__name:"Settings",async setup(d){let s,o;const g=[{text:"Settings"}],c=z(),[a,r]=([s,o]=V(()=>Promise.all([c.admin.getSettings(),c.admin.getVersion()])),s=await s,o(),s);return j("Settings"),(q,m)=>{const S=l("p-key-value"),b=l("p-theme-toggle"),_=l("p-label"),k=l("p-layout-default");return i(),w(k,{class:"settings"},{header:t(()=>[e(n(P),{crumbs:g},{actions:t(()=>[e(S,{class:"settings__version",label:"Version",value:n(r),alternate:""},null,8,["value"])]),_:1})]),default:t(()=>[e(_,{label:"Theme"},{default:t(()=>[e(b)]),_:1}),e(_,{label:"Color Mode",class:"settings__color-mode"},{default:t(()=>[e(n(A),{selected:n(p),"onUpdate:selected":m[0]||(m[0]=y=>F(p)?p.value=y:null)},null,8,["selected"])]),_:1}),e(_,{label:"Server Settings"},{default:t(()=>[e(T,{class:"settings__code-block","engine-settings":n(a)},null,8,["engine-settings"])]),_:1})]),_:1})}}});export{O as default};
//# sourceMappingURL=Settings-CoD_nshr.js.map
