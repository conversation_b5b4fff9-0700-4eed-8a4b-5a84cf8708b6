import{d as I,h as N,W as R,i as S,j as p,ae as T,al as V,G as _,aG as $,k as r,c,o as l,l as e,t as D,a as i,m as s,p as t,aH as G,aI as H,aJ as d,B as L,v as W,aK as v,a6 as j,aL as m,aM as E}from"./index-CUm6gmtO.js";import{u as F}from"./usePageTitle-BEMSydIZ.js";const J={key:0},U=I({__name:"Artifact",setup(K){const k=N(),b=R("artifactId"),w=S(k.artifacts.getArtifact,[b]),a=p(()=>w.response),n=T(!1),y=[{label:"Artifact"},{label:"Details"},{label:"Raw"}],u=V("tab","Artifact"),C=p(()=>a.value?`${_.info.artifact}: ${a.value.key??$(a.value.type)}`:_.info.artifact);return F(C),(M,o)=>{const x=r("p-divider"),A=r("p-button"),g=r("p-content"),B=r("p-tabs"),h=r("p-layout-well");return l(),c(h,{class:"artifact"},{header:e(()=>[a.value?(l(),c(t(E),{key:0,artifact:a.value},null,8,["artifact"])):i("",!0)]),well:e(()=>[a.value?(l(),c(t(m),{key:0,artifact:a.value,alternate:""},null,8,["artifact"])):i("",!0)]),default:e(()=>[a.value?(l(),D("section",J,[s(t(G),{artifact:a.value},null,8,["artifact"]),s(x),t(H).xl?(l(),c(g,{key:0},{default:e(()=>[s(t(d),{artifact:a.value},null,8,["artifact"]),s(A,{class:"artifact__raw-data-button",small:"",onClick:o[0]||(o[0]=f=>n.value=!n.value)},{default:e(()=>[L(W(n.value?"Hide":"Show")+" raw data ",1)]),_:1}),n.value?(l(),c(t(v),{key:0,artifact:a.value},null,8,["artifact"])):i("",!0)]),_:1})):(l(),c(B,{key:1,selected:t(u),"onUpdate:selected":o[1]||(o[1]=f=>j(u)?u.value=f:null),tabs:y},{artifact:e(()=>[s(t(d),{artifact:a.value},null,8,["artifact"])]),details:e(()=>[s(t(m),{artifact:a.value},null,8,["artifact"])]),raw:e(()=>[s(t(v),{artifact:a.value},null,8,["artifact"])]),_:1},8,["selected"]))])):i("",!0)]),_:1})}}});export{U as default};
//# sourceMappingURL=Artifact-CLM2loue.js.map
