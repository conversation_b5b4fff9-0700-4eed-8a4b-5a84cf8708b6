import{d as f,h as i,i as _,j as o,k as w,c as s,o as t,l as n,t as C,a as k,F as b,p as a,bM as h,bN as x,m as y,bO as v}from"./index-CUm6gmtO.js";import{u as F}from"./usePageTitle-BEMSydIZ.js";const O=f({__name:"Flows",setup(g){const l=i(),c={interval:3e4},e=_(l.flows.getFlowsCount,[{}],c),r=o(()=>e.response??0),u=o(()=>e.executed&&r.value===0),p=o(()=>e.executed),m=()=>{e.refresh()};return F("Flows"),(B,N)=>{const d=w("p-layout-default");return t(),s(d,{class:"flows"},{header:n(()=>[y(a(v))]),default:n(()=>[p.value?(t(),C(b,{key:0},[u.value?(t(),s(a(h),{key:0})):(t(),s(a(x),{key:1,selectable:"",onDelete:m}))],64)):k("",!0)]),_:1})}}});export{O as default};
//# sourceMappingURL=Flows-CpVj5BuR.js.map
